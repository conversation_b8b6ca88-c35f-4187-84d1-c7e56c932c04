<div class="card p-3 mb-3">
  <table class="table skeleton-table">
    <thead>
      <tr class="d-none d-lg-table-row">
        <th scope="col">
          <span class="fw-bold text-dark text-nowrap">PO Number</span>
        </th>
        <th scope="col">
          <span class="fw-bold text-dark text-nowrap">Date</span>
        </th>
        <th scope="col">
          <span class="fw-bold text-dark text-nowrap">Project</span>
        </th>
        <th scope="col">
          <span class="fw-bold text-dark text-nowrap">Items</span>
        </th>
        <th scope="col">
          <span class="fw-bold text-dark text-nowrap">Total</span>
        </th>
        <th scope="col">
          <span class="fw-bold text-dark text-nowrap">Carrier</span>
        </th>
        <th scope="col">
          <span class="fw-bold text-dark text-nowrap"></span>
        </th>
      </tr>
    </thead>
    <tbody class="placeholder-glow">
      <tr *ngFor="let row of skeletonRows; trackBy: trackByIndex" class="skeleton-row">
        <!-- Mobile View (stacked) -->
        <td class="d-lg-none skeleton-cell">
          <div class="mb-1">
            <span class="placeholder col-6" style="height: 16px;"></span>
          </div>
          <div class="mb-1">
            <span class="placeholder col-4" style="height: 14px;"></span>
          </div>
          <div class="mb-1">
            <span class="placeholder col-8" style="height: 16px;"></span>
          </div>
          <div class="mb-1">
            <span class="placeholder col-5" style="height: 14px;"></span>
          </div>
          <div class="mb-1">
            <span class="placeholder col-4" style="height: 12px;"></span>
          </div>
          <div>
            <span class="placeholder" style="width: 40px; height: 32px; border-radius: 4px;"></span>
          </div>
        </td>
        
        <!-- Desktop View (table columns) -->
        <td class="d-none d-lg-table-cell align-middle skeleton-cell">
          <span class="placeholder col-8" style="height: 16px;"></span>
        </td>
        <td class="d-none d-lg-table-cell align-middle skeleton-cell">
          <span class="placeholder col-6" style="height: 14px;"></span>
        </td>
        <td class="d-none d-lg-table-cell align-middle skeleton-cell">
          <span class="placeholder col-10" style="height: 16px;"></span>
        </td>
        <td class="d-none d-lg-table-cell align-middle skeleton-cell">
          <span class="placeholder col-3" style="height: 16px;"></span>
        </td>
        <td class="d-none d-lg-table-cell align-middle skeleton-cell">
          <span class="placeholder col-7" style="height: 16px;"></span>
        </td>
        <td class="d-none d-lg-table-cell align-middle skeleton-cell">
          <span class="placeholder col-8" style="height: 12px;"></span>
        </td>
        <td class="d-none d-lg-table-cell align-middle text-end skeleton-cell">
          <span class="placeholder" style="width: 40px; height: 32px; border-radius: 4px;"></span>
        </td>
      </tr>
    </tbody>
  </table>

  <!-- Skeleton Pagination -->
  <div class="d-flex justify-content-between align-items-center mt-3 flex-wrap">
    <div class="text-muted mb-2 mb-sm-0">
      <span class="placeholder col-8" style="height: 14px;"></span>
    </div>
    <div>
      <span class="placeholder" style="width: 200px; height: 32px; border-radius: 4px;"></span>
    </div>
  </div>
</div>
