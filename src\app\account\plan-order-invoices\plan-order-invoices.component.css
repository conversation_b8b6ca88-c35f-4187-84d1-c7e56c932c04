/* Minimal custom styles - using Bootstrap for everything else */

/* Table styling improvements */
.table th {
  border-top: none;
  font-weight: 600;
  background-color: #f8f9fa;
}

.table-responsive {
  border-radius: 0;
}

/* Header styling */
h1 {
  color: #343a40;
  font-weight: 600;
}

/* Loading and error states */
.spinner-border {
  width: 3rem;
  height: 3rem;
}

.alert-danger {
  border-color: #dc3545;
}

.alert-heading {
  color: #721c24;
}

/* Responsive improvements */
@media (max-width: 768px) {
  .table {
    font-size: 14px;
  }
  
  .d-flex.justify-content-between {
    flex-direction: column;
    align-items: flex-start !important;
  }
  
  .d-flex.justify-content-between .btn {
    margin-bottom: 0.5rem;
    width: 100%;
  }
}
