import { Component, OnInit, inject, computed, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { toSignal } from '@angular/core/rxjs-interop';
import { map } from 'rxjs/operators';
import { PlanOrderInvoiceService } from './plan-order-invoice.service';
import { PlanOrdersService } from 'src/app/bid-opportunities/bid/plan-orders/plan-orders.service';
import { InvoiceSkeletonComponent } from './invoice-skeleton/invoice-skeleton.component';

@Component({
  selector: 'app-plan-order-invoice',
  imports: [CommonModule, InvoiceSkeletonComponent],
  templateUrl: './plan-order-invoice.component.html',
  styleUrl: './plan-order-invoice.component.css'
})
export class PlanOrderInvoiceComponent implements OnInit {
  /////////// Services //////////
  private readonly route = inject(ActivatedRoute);
  private readonly router = inject(Router);
  private readonly planOrderInvoiceService = inject(PlanOrderInvoiceService);
 

  /////////// Route Parameters //////////
  readonly invoiceId = toSignal(
    this.route.paramMap.pipe(
      map(params => params.get('invoiceId'))
    )
  );

  /////////// Computed from Service //////////
  readonly receiptData = this.planOrderInvoiceService.invoiceData;
  readonly isLoading = this.planOrderInvoiceService.isLoading;
  readonly error = this.planOrderInvoiceService.error;

  /////////// Effects //////////
  constructor() {
    // Effect to load invoice when route parameter changes
    effect(() => {
      const id = this.invoiceId();
      if (id) {
        this.planOrderInvoiceService.setInvoiceId(id);
      } else {
        this.planOrderInvoiceService.clearInvoice();
      }
    });

   
  }

  ngOnInit(): void {
    // The effect will handle loading the invoice based on route params
  }

  /////////// Navigation Methods //////////
  goBack(): void {
    this.router.navigate(['/account/plan-order-invoices']);
  }

  printInvoice(): void {
    window.print();
  }

  /////////// Utility Methods //////////
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }
}
