import { Injectable, signal, computed } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, catchError, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { EBidHistory } from '../interfaces/ebid-history.model';

@Injectable({
  providedIn: 'root',
})
export class OnlineEBidHistoryService {
  private readonly apiUrl = environment.services_root_endpoints.eBidHistory;

  eBidHistory = signal<EBidHistory[]>([]);
  isLoading = computed(() => this.eBidHistory().length === 0);

  constructor(private client: HttpClient) {}

  fetchEBidHistory(): Observable<EBidHistory[]> {
    return this.client.get<EBidHistory[]>(this.apiUrl).pipe(
      catchError((error) => {
        console.error('Error fetching eBid history:', error);
        return throwError(() => new Error('Failed to fetch eBid history.'));
      })
    );
  }

  loadEBidHistory(): void {
    this.fetchEBidHistory().subscribe({
      next: (data) => this.eBidHistory.set(data),
      error: (err) => {
        console.error('Failed to load eBid history:', err);
      },
    });
  }
}
