import { Injectable, inject, signal, computed, effect, Signal, ResourceLoaderParams } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, catchError, tap, throwError, map, of } from 'rxjs';
import { RoleSettingsDiaryModel } from '../../user/shared/interfaces/role-settings-diary';
import { rxResource } from '@angular/core/rxjs-interop';
import { DiarySectionType, VisibilityType, ALL_DIARY_SECTIONS } from '../../user/shared/enums/diary-section.enum';

/**
 * Manages role-specific diary settings, handling fetching, saving, and reactive state.
 * Uses rxResource to load settings by Role ID and signals for sections, loading, and errors.
 * Provided in 'root' for application-wide access to diary settings state.
 */
@Injectable({
  providedIn: 'root'
})
export class RoleSettingsDiaryService {
  private readonly http = inject(HttpClient);
  private readonly apiUrl = `${environment.services_root_endpoints.role_settings}/`;
  
  private readonly _diarySections = signal<DiarySection[]>([]);
  private readonly _isLoading = signal<boolean>(false);
  private readonly _isSaving = signal<boolean>(false);
  private readonly _currentRoleId = signal<string | null>(null);
  private readonly _error = signal<string | null>(null);
  
  // Public read-only signals
  readonly diarySections = this._diarySections.asReadonly();
  readonly isLoading = this._isLoading.asReadonly();
  readonly isSaving = this._isSaving.asReadonly();
  readonly currentRoleId = this._currentRoleId.asReadonly();
  readonly error = this._error.asReadonly();
  
  private readonly defaultSections = ALL_DIARY_SECTIONS;
  
  roleSettingsResource = rxResource({
    request: () => this._currentRoleId(),
    loader: (params: ResourceLoaderParams<string | null>) => {
      const roleId = params.request;
      
      // If roleId is null or empty, use the base apiUrl (without appending a roleId)
      if (!roleId || typeof roleId !== 'string' || roleId.trim() === '') {
        return this.http.get<RoleSettingsDiaryModel>(this.apiUrl);
      }
      
      const url = `${this.apiUrl}${roleId}`;
      return this.http.get<RoleSettingsDiaryModel>(url);
    }
  });

  constructor() {
    // Use effect to handle resource loading state
    effect(() => {
      this._isLoading.set(this.roleSettingsResource.isLoading());
    });
    
    // Use effect to handle resource value
    effect(() => {
      const response = this.roleSettingsResource.value();
      if (response) {
        this.processRoleSettingsResponse(response);
      }
    });
    
    // Use effect to handle resource error
    effect(() => {
      const error = this.roleSettingsResource.error();
      if (error) {
        this.handleError(error);
      }
    });
  }
  
  /**
   * Sets the current role ID and triggers a fetch of role settings only if the role ID has changed
   * This prevents unnecessary API calls when the role ID hasn't changed
   */
  setRoleId(roleId: string | null): void {
    // Normalize the role ID
    const newRoleId = roleId && typeof roleId === 'string' && roleId.trim() !== '' 
      ? roleId 
      : null;
    
    const currentRoleId = this._currentRoleId();
    
    // Only update the signal if the role ID has changed
    if (currentRoleId !== newRoleId) {
      this._error.set(null); // Clear error state
      this._currentRoleId.set(newRoleId); // This change will trigger rxResource automatically
    }
  }

  /**
   * Refreshes role settings from the server
   * This is the only method that should be used to manually force a reload
   * @param roleId Optional role ID to fetch settings for
   * @returns An Observable that completes when settings are refreshed
   */
  refreshRoleSettings(roleId?: string): Observable<void> {
    this._error.set(null);
    
    if (roleId) {
      // Normalize the role ID
      const newRoleId = roleId && typeof roleId === 'string' && roleId.trim() !== '' 
        ? roleId 
        : null;
        
      this._currentRoleId.set(newRoleId);
    }
    
    // Explicitly force a reload regardless of whether the role ID changed
    // This is the only place where we should call reload() directly
    this.roleSettingsResource.reload();
    return of(void 0);
  }
  
  /**
   * Formats a section name with Show/Hide prefix
   * @param section The section name (e.g., 'Notes', 'Photos')
   * @param value The visibility value (VisibilityType.SHOW or VisibilityType.HIDE)
   * @returns Formatted setting name (e.g., 'ShowNotes', 'HidePhotos')
   */
  private formatSettingName(section: string, value: VisibilityType | string): string {
    return `${value}${section.replace(/\s+/g, '')}`;
  }
  

  /**
   * Processes the role settings response and updates the diary sections signal
   */
  private processRoleSettingsResponse(response: RoleSettingsDiaryModel, componentName: string = 'Diary'): void {
    // Validate response structure
    if (!response || !response.Settings || !Array.isArray(response.Settings)) {
      this._error.set('Invalid settings response received'); // Set error message
      this._diarySections.set(this.defaultSections.map(s => ({ 
        Component: s, 
        SettingValue: VisibilityType.SHOW 
      })));
      return;
    }
  
    const settings = response.Settings.find(cs => cs.ComponentName === componentName) || 
                    { ComponentName: componentName, Settings: [] };
    
    // Validate settings.Settings array
    const transformedSettings = this.defaultSections.map(section => ({
      Component: section,
      SettingValue: Array.isArray(settings.Settings) && 
                   settings.Settings.includes(this.formatSettingName(section.toString(), VisibilityType.HIDE)) 
                   ? VisibilityType.HIDE 
                   : VisibilityType.SHOW
    }));
    
    this._diarySections.set(transformedSettings.length ? transformedSettings : 
      this.defaultSections.map(s => ({ 
        Component: s, 
        SettingValue: VisibilityType.SHOW 
      })));
  }

  /**
   * Saves role settings for the specified role ID
   * @param roleId The role ID to save settings for
   * @param settings The settings to save
   * @returns An Observable that completes when settings are saved
   */
  saveRoleSettings(roleId: string, settings: RoleSettingsDiaryModel): Observable<void> {
    this._isSaving.set(true);
    this._error.set(null);
    
    return this.http.post<void>(`${this.apiUrl}${roleId}`, settings).pipe(
      tap(() => {
        this._isSaving.set(false);
        
        // If the saved roleId matches the current roleId, update the local state directly
        // This prevents an unnecessary API call and screen flickering
        if (roleId === this._currentRoleId()) {
          // Extract the diary sections from the settings payload
          // This is more efficient than reloading from the server
          this.updateLocalDiarySections(settings);
        }
      }),
      catchError(error => {
        this._isSaving.set(false);
        return this.handleError(error);
      })
    );
  }
  
  /**
   * Updates the local diary sections state from a settings payload
   * This is used to avoid an extra API call after saving
   * @param settings The settings payload that was saved
   */
  private updateLocalDiarySections(settings: RoleSettingsDiaryModel): void {
    if (!settings || !settings.Settings || !Array.isArray(settings.Settings)) {
      return;
    }
    
    const diarySettings = settings.Settings.find(cs => cs.ComponentName === 'Diary');
    if (!diarySettings || !Array.isArray(diarySettings.Settings)) {
      return;
    }
    
    // Transform the settings into DiarySection objects
    const transformedSettings = this.defaultSections.map(section => {
      const settingName = this.formatSettingName(section.toString(), VisibilityType.HIDE);
      const isHidden = diarySettings.Settings.includes(settingName);
      
      return {
        Component: section,
        SettingValue: isHidden ? VisibilityType.HIDE : VisibilityType.SHOW
      };
    });
    
    this._diarySections.set(transformedSettings);
  }
  
  /**
   * Returns a signal indicating whether a section is visible
   * @param section The section name (e.g., 'Notes', 'Photos')
   * @returns A signal emitting true if 'Show', false if 'Hide' or not found
   */
  getSectionVisibility(section: string): Signal<boolean> {
    return computed(() => {
      const found = this.diarySections().find(s => s.Component === section);
      return found ? found.SettingValue === VisibilityType.SHOW : false;
    });
  }

  /**
   * Creates a payload for saving role settings
   * @param roleId The role ID
   * @param sections The diary sections
   * @param componentName The component name (defaults to 'Diary')
   * @returns A payload object for the API
   */
  createSavePayload(roleId: string, sections: DiarySection[], componentName: string = 'Diary'): RoleSettingsDiaryModel {
    const updatedSettings = sections.map(section => 
      this.formatSettingName(
        section.Component.toString(), 
        section.SettingValue as VisibilityType
      )
    );
    
    return {
      RoleId: roleId,
      Settings: [{ ComponentName: componentName, Settings: updatedSettings }]
    };
  }
  
  /**
   * Handles HTTP errors
   */
  private handleError(error: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred.';
    
    if (error instanceof HttpErrorResponse) {
      errorMessage = `Error ${error.status}: ${error.message}`;
    }
    
    this._error.set(errorMessage);
    
    return throwError(() => error);
  }
}

export interface DiarySection {
  Component: DiarySectionType | string;
  SettingValue: VisibilityType | string;
}