{"SecretManager": {"SecretName": "dev/construction-management/web-api", "ConnectionStringKey": "mongo-connection/bidops-projects"}, "AppSettings": {"Connection": "/secret/", "CivCastDatabase": "CivCast", "EmailCollection": "email-logs", "EmailFrom": "<EMAIL>", "EmailTemplatesS3Bucket": "civcast-app-dev", "EmailTemplateInfos": [{"EmailId": "ContractorA<PERSON>dum<PERSON><PERSON><PERSON>", "S3Key": "email-templates/ContractorAddendumAlert.json", "CloudFrontUrl": "https://d1q2w3e4r5t6.cloudfront.net/email-templates/ContractorAddendumAlert.json"}, {"EmailId": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "S3Key": "email-templates/BidAlert.json", "CloudFrontUrl": "https://d1q2w3e4r5t6.cloudfront.net/email-templates/BidAlert.json"}, {"EmailId": "ProjectUserHistory", "S3Key": "email-templates/ProjectUserHistory.json", "CloudFrontUrl": "https://d1q2w3e4r5t6.cloudfront.net/email-templates/ProjectUserHistory.json"}, {"EmailId": "test-email", "S3Key": "email-templates/test-email.json", "CloudFrontUrl": "https://d1q2w3e4r5t6.cloudfront.net/email-templates/test-email.json"}, {"EmailId": "system-test-template", "S3Key": "email-templates/system-test-template.json", "CloudFrontUrl": "https://d1q2w3e4r5t6.cloudfront.net/email-templates/system-test-template.json"}], "UserProjectHistoryTopic": "arn:aws:sns:us-east-1:570139678931:cm-projectuserhistory-dev-insert-projectuserhistory"}}