import { Injectable, computed, inject, signal } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { of, Subscription } from 'rxjs';
import { Planholder } from '../interfaces/bids-planholder';
import { rxResource } from '@angular/core/rxjs-interop';

@Injectable()
export class BidAdvertsPlanholdersService {
	client = inject(HttpClient);
	planholdersGrid = computed(() => this.planholdersResource.value());
	isLoading = computed(() => this.planholdersResource.isLoading());
	planholdersSubscription: Subscription | null = null;
	planholdersIsLoading = signal<boolean>(false);
	page = signal<number>(1);	
	search = signal<string | null>(null);	
	sortBy = signal<string>("CompanyName");			
	limit = signal<number>(50);
	projectId = signal<string | null>(null);
	isReversed = signal<boolean>(false);

	destroy() {
		this.planholdersResource.set(null);		
		this.projectId.set(null);
	}

	planholdersResource = rxResource({
		request: () => ({
			projectId: this.projectId(),
			page: this.page(),
			search: this.search(),
			sortBy: this.sortBy(),			
			limit: this.limit(),
			isReversed: this.isReversed(),
		}),
		loader: (request) => {
			if(request.request.projectId) {
				var phRequest = new PlanholdersRequest();
				phRequest.Page = this.page();
				phRequest.Search = this.search();
				phRequest.SortBy = this.sortBy();
				phRequest.Limit = this.limit();	
				phRequest.SortOrder = this.isReversed() ? 'desc' : 'asc'
				
				let queryString = Object.entries(phRequest)
				.filter(([key, value]) => value !== null)
				.map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
				.join('&');

				var url = `${environment.services_root_endpoints.adverts_planholders}/${this.projectId()}?${queryString}`;

				return this.client.get<PlanholdersGrid>(url);
			}

			return of(null);
		},
	});
}

export interface PlanholdersGrid {
	Planholders: Array<Planholder>;
	Total: number;
}

export class PlanholdersRequest {
	Page: number = 1;	
	Search: string | null = null;	
	SortBy: string = "DateCreated";		
	SortOrder: string = 'desc';	
	Limit: number = 25;
}