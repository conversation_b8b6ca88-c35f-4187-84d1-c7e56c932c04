import { HttpClient } from "@angular/common/http";
import { Injectable, computed, effect, inject, signal } from "@angular/core";
import { Answer, Question } from "../interfaces/questions";
import { of, Subscription } from "rxjs";
import { environment } from "src/environments/environment";
import { BidsAdvService } from "./bids.service";
import { rxResource } from "@angular/core/rxjs-interop";
import { HelperTools } from "src/app/shared/utils/helper-tools";
import { FormGroup, FormControl, Validators } from "@angular/forms";
import { CivCastAccountService } from "src/app/shared/data-access/civcast-account-service";
import { AllowedProfile } from "src/app/shared/interfaces/account-profile";
import { BidAdvService } from "./bid.service";

@Injectable()
export class BidsAdvQAService {
	client = inject(HttpClient);
	bidsAdvService = inject(BidAdvService);
	civcastAccountService = inject(CivCastAccountService);
	projectQuestions = computed(() => this.projectQuestionsResource.value());
	projectId = signal<string | null>(null);
	questionsFormatted = signal<Array<QuestionInfo>>([]);
	excelDownloadSubscription: Subscription | null = null;
	isAskingQuestion = signal<boolean>(false);
	isDownloadingExcel = signal<boolean>(false);
	isLoading = computed(() => this.projectQuestionsResource.isLoading());
	questionForm = new FormGroup({
		question: new FormControl('', Validators.required)
	});

	answerForms = new Map<string, FormGroup>();
	addAnswerToQuestionInfo = signal<string[]>([]);

	constructor() {

		effect(() => {
			if (this.projectQuestions()) {
				var allQuestions = [];
				for (let question of this.projectQuestions() as Question[]) {
					var questionInfo = new QuestionInfo(question);
					allQuestions?.push(questionInfo);
				}

				this.questionsFormatted.set(allQuestions);
			}
		});

		effect(() => {
			if (this.questionsFormatted()) {
				for (var fQuestion of this.questionsFormatted()) {
					if (fQuestion.question.Answers) {
						for (let answer of fQuestion.question.Answers.filter(x => !x.IsFormatted)) {
							answer.Content = HelperTools.wrapImagesWithLinks(answer.Content as string);
							answer.IsFormatted = true;
						}
					}

					var cognitoUserIds = new Array<string>();

					if (!fQuestion.profileLoaded) {
						if (cognitoUserIds.indexOf(fQuestion.question.CognitoUserId || fQuestion.question.UserId || '') === -1) {
							cognitoUserIds.push(fQuestion.question.CognitoUserId || fQuestion.question.UserId || '');
						}
					}


					if (cognitoUserIds.length > 0) {
						this.civcastAccountService.getUserProfiles(cognitoUserIds);
					}
				}
			}
		});


		effect(() => {
			if (this.civcastAccountService.userProfiles()) {
				// Update questionsFormatted in-place without triggering consumers
				const questions = this.questionsFormatted();
				for (let question of questions) {
					const profile = this.civcastAccountService.userProfiles()?.find(
						profile =>
							profile.CognitoUserId === question.question.CognitoUserId ||
							profile.UserId == question.question.UserId
					);
					if (profile) {
						question.profileInfo = profile as AllowedProfile;					
					}else{
						question.profileInfo = {
							FirstName: "User",							
							CompanyName: "Not Found",
						} as AllowedProfile;
					}

					question.profileLoaded = true;
				}
			}
		});
	}

	destroy() {
		this.excelDownloadSubscription?.unsubscribe();
	}


	addAnswerForm(questionId: string, answerForm: FormGroup<{ answer: FormControl<string | null>; }>) {
		if (!this.answerForms.has(questionId)) {
			this.answerForms.set(questionId, answerForm);
		}
	}

	removeAnswerForm(questionId: string) {
		if (this.answerForms.has(questionId)) {
			this.answerForms.delete(questionId);
		}
	}

	projectQuestionsResource = rxResource({
		request: () => (
			this.projectId()),
		loader: (request) => {
			if (request.request) {
				return this.client.get<Array<Question>>(`${environment.services_root_endpoints.adverts_qa}/${request.request}`)
			}

			return of(null);
		}
	});


	exportQuestionsExcel() {
		this.isDownloadingExcel.set(true);

		this.excelDownloadSubscription = this.client.get<QAExcelResponse>(`${environment.services_root_endpoints.adverts_qa}/${this.projectId()}?method=excel&project-title=${this.bidsAdvService.advertisement()?.ProjectTitle}`).subscribe({
			next: (res) => {
				this.isDownloadingExcel.set(false);
				document.location.href = res.Url;
			},
			error: (err) => {
				this.isDownloadingExcel.set(false);
				console.error(err);
			}
		});

	}


	addQuestion(question: string) {
		this.isAskingQuestion.set(true);

		this.client.post<Question>(`${environment.services_root_endpoints.adverts_qa}/${this.projectId()}`, question).subscribe({
			next: (res) => {
				this.projectQuestionsResource.update(questions => {
					if (questions) {
						res.Answers = [];
						questions.push(res);
					}

					return [...questions as []];
				});

				this.bidsAdvService.advertisementResource.update((advertisement) => {
					if (advertisement) {
						advertisement.QuestionCount = advertisement.QuestionCount + 1;
						return { ...advertisement };
					}
					return advertisement;
				});

				this.questionForm.get('question')?.setValue('');

				this.isAskingQuestion.set(false);
			},
			error: (err) => {
				this.isAskingQuestion.set(false);
				console.error(err);
			}
		});
	}

	addAnswer(questionId: string, answer: string) {
		this.addAnswerToQuestionInfo.update(questions => {
			if (!questions.includes(questionId)) {
				questions.push(questionId);
			}
			return [...questions];
		});



		this.client.post<Answer>(`${environment.services_root_endpoints.adverts_qa}/${this.projectId()}/${questionId}`, { Content: answer }).subscribe({
			next: (res) => {
				this.projectQuestionsResource.update(questions => {
					var question = questions?.find(q => q.QuestionId === questionId);
					if (question) {
						question.Answers.push(res);
					}
					return [...questions as []];
				});


				this.bidsAdvService.advertisementResource.update((advertisement) => {
					if (advertisement) {
						advertisement.AnswerCount = advertisement.AnswerCount + 1;
						return { ...advertisement };
					}
					return advertisement;
				});

				this.addAnswerToQuestionInfo.update(questions => {
					var index = questions.indexOf(questionId);
					if (index > -1) {
						questions.splice(index, 1);
					}
					return [...questions];
				});

				var answerForm = this.answerForms.get(questionId);
				if (answerForm) {
					answerForm.get('answer')?.setValue('');
				}
			},
			error: (err) => {
				console.error(err);
			}
		});
	}

	isAddingAnswer(questionId: string): boolean {
		return this.addAnswerToQuestionInfo().includes(questionId);
	}

	updateAnswer(questionId: string, answer: Answer, updateOptions: AnswerUpdateOptions) {
		answer.IsStrikingOut = true;
		this.client.patch<Answer>(`${environment.services_root_endpoints.adverts_qa}/${this.projectId()}/${questionId}/${answer?.AnswerId}`, updateOptions).subscribe({
			next: (res: Answer) => {
				this.projectQuestionsResource.update(questions => {
					var question = questions?.find(q => q.QuestionId === questionId);
					if (question) {
						if (answer) {
							if (updateOptions.Disabled !== null) {
								answer.IsDisabled = updateOptions.Disabled;
							}
						}
					}
					return [...questions as []];
				});

				if (updateOptions.Disabled) {
					this.bidsAdvService.advertisementResource.update((advertisement) => {
						if (advertisement) {
							advertisement.AnswerCount = advertisement.AnswerCount - 1;
							return { ...advertisement };
						}
						return advertisement;
					});
				} else {
					this.bidsAdvService.advertisementResource.update((advertisement) => {
						if (advertisement) {
							advertisement.AnswerCount = advertisement.AnswerCount + 1;
							return { ...advertisement };
						}
						return advertisement;
					});
				}

				answer.IsStrikingOut = false;
			},
			error: (err) => {
				answer.IsStrikingOut = false;
				console.error(err);
			}
		});
	}
}

export class AddAnswerInfo {
	questionId: string;
	answerContent: string;
	projectId: string;
	answerId: string;

	constructor(questionId: string, answerContent: string, projectId: string, answerId: string) {
		this.questionId = questionId;
		this.answerContent = answerContent;
		this.projectId = projectId;
		this.answerId = answerId;
	}
}

export class AnswerUpdateOptions {
	constructor(disabled: boolean | null = null) {
		this.Disabled = disabled;
	}
	Disabled: boolean | null = null;
}


export interface QAExcelResponse {
	Url: string;
}


export class AnswerUpdateInfo {
	questionId: string;
	answerId: string;
	isDisabled: boolean;

	constructor(questionId: string, answerId: string, isDisabled: boolean) {
		this.questionId = questionId;
		this.answerId = answerId;
		this.isDisabled = isDisabled;
	}
}

export class QuestionInfo {
	question: Question;
	answerContent: string | null = null;
	showAnswer: boolean = false;
	isAddingAnswer: boolean = false;
	profileInfo: AllowedProfile | null = null;
	profileLoaded: boolean = false;

	constructor(question: Question) {
		this.question = question;
	}
}

export class AnswerAddInfo {
	questionId: string;
	questionContent: string

	constructor(questionId: string, questionContent: string) {
		this.questionId = questionId;
		this.questionContent = questionContent;
	}
}