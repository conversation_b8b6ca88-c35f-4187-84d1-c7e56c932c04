import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { InfoItem, ProjectInfoComponent } from 'src/app/construction/shared/interfaces/info';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { of, switchMap, tap } from 'rxjs';
import { ConstructionInfoService } from 'src/app/construction/shared/data-access/info.service';
import { ActivatedRoute, Router } from '@angular/router';
import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { UserProjectComponentsService } from 'src/app/construction/shared/data-access/user-project-components.service';

@Component({
    selector: 'app-info',
    imports: [CommonModule, CdkDropList, CdkDrag],
    templateUrl: './info.component.html',
    styleUrl: './info.component.css'
})
export class ProjectGlobalSettingsInfoComponent {
  infoComponentService = inject(ConstructionInfoService);
  isLoading = signal<boolean>(false);
  infoComponent = this.infoComponentService.infoComponent;
  filter = signal<string>('');
  router = inject(Router);
  aRoute = inject(ActivatedRoute);



  
  private infoComponentReadOnly = toSignal(toObservable(this.filter).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap((filter) => this.infoComponentService.getUserStoreInfoComponent()),
    tap((result: ProjectInfoComponent | null) => {
      if(result){
        this.infoComponent.set(result);
      }else{
        this.infoComponent.set(null);
      }
    }),
    tap(() => this.isLoading.set(false))
  ));
  
  saveComponent(){
    of(this.infoComponent()).pipe(
      tap((request) => {
        this.isLoading.set(true);
      }),
      switchMap(projectComponent =>
        projectComponent ? this.infoComponentService.saveUserInfoComponent(projectComponent as ProjectInfoComponent) : of(null)  
      ),
      tap((component: ProjectInfoComponent | null) => {
        this.isLoading.set(false);
    
      })
    ).subscribe();
     
  }
  add(){
    this.router.navigate(['add'], {relativeTo: this.aRoute});
  }

  hide(item: InfoItem){
    this.infoComponent.update((x) => {
        var prop = x?.Properties.find(x => x.PropertyId === item.PropertyId);
        if(prop){
          prop.IsHidden = !prop.IsHidden;
        }

        return x;        
    });    
  }

  remove(item: InfoItem){
    this.infoComponent.update((component) => {
      if(component){
        component.Properties = [...component.Properties.filter(x => x.PropertyId !== item.PropertyId)];
      }

      return component;

      });
  }

  edit(item: InfoItem){
    this.router.navigate(['edit', item.PropertyId], {relativeTo: this.aRoute});   
  }

  drop(event: CdkDragDrop<string[]>) {
    if(this.infoComponent()){
      moveItemInArray(this.infoComponent()!.Properties, event.previousIndex, event.currentIndex);
    }
    
  }
}
