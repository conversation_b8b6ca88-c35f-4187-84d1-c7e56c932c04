import { AccountProfile } from "src/app/shared/interfaces/account-profile";

export interface AdvertisementInfo{
	Id: string;
	ProjectTitle: string;
	IsPrivate: boolean;
	BidDate: Date;
	IsTBA: boolean;
	InternalId: string;

}

export interface Advertisement {
	Id: string | null;
	ProjectTitle: string;
	CountyName: string;
	StateName: string;
	InternalId: string;
	ProjectUniqueId: string;
	DetailsEstimate: string;
	TypeOfWork: string;
	Owner:string;
	Password: string;
	Notes: string;
	MapCode:string;
	Scope:string;
	AdditionalNotes:string;
	CustomMapUrl: string;
	Location: Location;
	ReleaseDateTimeInfo: TimeInfo;
	ReleaseDateMonthNatural: number;
	BidDetails: BidDateTime;
	BidDateMonthNatural: number;
	PreBidDetails: BidDateTime;
	PreBidDateMonthNatural: number;
	Permissions: ProjectPermissions;
	ContactInfo: AccountProfile;
	PaymentDate: Date | null;
	QAExpirationDateTimeInfo: TimeInfo;
	QAExpirationDateMonthNatural: number;
	QuestionEmails: Array<EmailAddress>;
	// Planholders: Array<Planholder>;
	// Questions: Array<Question>;
	IsTBA: boolean;
	IsBid: boolean;
	LowBidName: string;
	LowBidAmount: number;
	BidOpeningLink: string;
	PreBidMeetingLink: string;
	ReceivedBidsCount: number;
	PlanholderCount: number;
	QuestionCount: number;
	DocumentCount: number;
	AnswerCount: number;
	PaperBidsCount: number;
  }



  export interface ProjectPermissions{
	  ShowPlanholdersList:boolean;
	  AllowQuestions:boolean;
	  IsPrivate: boolean;
	  IsVisible: boolean;
	  IsPlansOfficial: boolean;
	  IsTBA: boolean;
	  ExpireQA: boolean;
  }
  
  export interface BidDateTime {
	BidDateTimeInfo: TimeInfo;
	Location: string;
	Notes: string;
	Display: string;
  }
  
  export interface TimeInfo {
	Date: string | null;
	Month: number | null;	
	Day: number | null;
	Year: number | null;
	Hour: number | null;
	Minute: number | null;
	TimeZone: TimeZone | null;
  }
  
  export interface TimeZone {
	ZoneId: string;
	Name: string;
	Value: string;
  }
  
  export interface Location {
	LocationId: string | null;
	State: State | null;
	County: County | null;
	MapData: MapData | null;
  }
  
  export interface County {
	CountyId: number;
	State: string;
	StateId: number;
	Name: string;
	Latitude: number;
	Longitude: number;
  }
  
  export interface MapData {
	Latitude: number;
	Longitude: number;
  }
  
  export interface State {
	StateId: number;
	Name: string;
	Abbreviation: string;
  }
  
  export interface EmailAddress{
	  DisplayName: string;
	  Email: string;
  }


