<div class="container p-4 mb-4">
  <!-- header -->
  <h1 class="fs-5 mb-3">Import Equipment</h1>
  <p>Import equipment from an Excel file. For the fastest import use our Excel template.</p>
  <!-- import equipment -->
  <section>
    @if(isLoading){
    <div>
      <div class="progress">
        <div class="progress-bar progress-bar-striped progress-bar-animated progress-bar-success" role="progressbar"
          [ngStyle]="{ 'width': '100%' }"></div>
      </div>
      <!-- <label style="text-align: center;">Updating equipment</label> -->
    </div>
    }@else {
    @switch (currentStep) {
    @case (0){
    <div class="col-12">
      <div class="btn-group">
        <civcast-aws-uploader [maxFiles]="1" (uploadError)="error($event)" (selectedFiles)="setSelectedFiles($event)"
          [allowDragDrop]="false" [allowFolderFiles]="false" [accept-info]="''"></civcast-aws-uploader>

        @if(equipmentImportSettings){
        <button class="btn btn-secondary" *ngIf="equipmentImportSettings" (click)="deleteSettings()">Remove
          Settings</button>
        }
      </div>
    </div>
    }
    @case (1) {
    <!-- case 1 -->
    <div class="border p-3 mb-3">
      <!-- step title -->
      <h2 class="page-title fs-4">Step 1</h2>
      <!-- worksheet selector -->
      <div class="mb-3">
        <label class="form-label">Which worksheet inside your Excel file do you want to use?</label>
        <select class="form-select w-50" [(ngModel)]="equipmentImportRequest.WorksheetName">
          @for (ws of equipmentImportResponse?.Worksheets; track $index) {
          <option [ngValue]="ws">
            {{ ws }}
          </option>
          }
        </select>
      </div>
      <!--instructions -->
      <div class="mb-3">For each field below, tell us which column in your Excel file contains that data.</div>
      <!--employee ID -->
      <div class="mb-3">
        <label class="form-label">Number</label>
        <select class="form-select w-50" [(ngModel)]="equipmentImportRequest.Number.Cell">
          @for (number of letterArray; track $index) {
          <option [ngValue]="number">
            {{ number }}
          </option>
          }

        </select>
      </div>
      <!-- first name -->
      <div class="mb-3">
        <label class="form-label">Make</label>
        <select class="form-select w-50" [(ngModel)]="equipmentImportRequest.Make.Cell">
          @for (number of letterArray; track $index) {
          <option [ngValue]="number">
            {{ number }}
          </option>
          }

        </select>
      </div>
      <!-- last name -->
      <div class="mb-3">
        <label class="form-label">Model</label>
        <select class="form-select w-50" [(ngModel)]="equipmentImportRequest.Model.Cell">
          @for (number of letterArray; track $index) {
          <option [ngValue]="number">
            {{ number }}
          </option>
          }
        </select>
      </div>
      <!-- classification -->
      <div class="mb-3">
        <label class="form-label">Notes</label>
        <select class="form-select w-50" [(ngModel)]="equipmentImportRequest.Notes.Cell">
          @for (number of letterArray; track $index) {
          <option [ngValue]="number">
            {{ number }}
          </option>
          }
        </select>
      </div>
      <!--header row? -->
      <div class="form-check">
        <input class="form-check-input" type="checkbox" [(ngModel)]="equipmentImportRequest.HasHeader" />
        <label class="form-check-label" for="flexCheckDefault">
          Has Header Row?
        </label>
      </div>

    </div>
    }
    @case(2){
    <!-- case 2 -->
    <div class="border p-3 mb-3">
      <!-- step title -->
      <h2 class="page-title fs-4">Step 2</h2>
      <!--instructions -->
      <div class="mb-3">
        Does the employee information below look correct? If not, return to the previous step.
      </div>
      <!--sample -->
      <div class="bg-light p-3">
        <div class="mb-3">
          <label class="fw-bold me-2">Number</label>
          <span>{{ sampleEquipment?.Number }}</span>
        </div>
        <div class="mb-3">
          <label class="fw-bold me-2">Make:</label>
          <span>{{ sampleEquipment?.Make }}</span>
        </div>
        <div class="mb-3">
          <label class="fw-bold me-2">Model:</label>
          <span>{{ sampleEquipment?.Model }}</span>
        </div>
        <div class="mb-3">
          <label class="fw-bold me-2">Notes:</label>
          <span>{{ sampleEquipment?.Notes }}</span>
        </div>
      </div>
    </div>
    }
    @case(3){
    <!-- case 3 -->
    <div class="border p-3 mb-3">
      <!-- step title -->
      <h2 class="page-title fs-4">Step 3</h2>
      <!--instructions -->
      <div class="mb-3">
        Do you want us to remember these import settings for next time?
      </div>
      <!--radio buttons - yes/no -->
      <div>
        <div class="form-check">
          <input class="form-check-input" name="settings" type="radio" [(ngModel)]="saveSettings" [value]="true"
            [checked]="saveSettings==true" />
          <label class="form-check-label" for="flexRadioDefault1">
            Yes
          </label>
        </div>
        <div class="form-check">
          <input class="form-check-input" name="settings" type="radio" [(ngModel)]="saveSettings" [value]="false"
            [checked]="saveSettings==false" />
          <label class="form-check-label" for="flexRadioDefault1">
            No
          </label>
        </div>
      </div>
    </div>
    }
    @case(4){
    <!-- case 4 -->
    <div class="border p-3 mb-3">
      <!-- no changes detected -->
      <div class="alert alert-info" role="alert"
        *ngIf="!importInfo?.EquipmentValidation?.length && !importInfo?.NewEquipments?.length">
        No changes detected in the import.
      </div>
      <!-- updating employees -->
      <div *ngIf="updateingEquipment">
        <div class="progress" *ngIf="updateingEquipment">
          <div class="progress-bar progress-bar-striped progress-bar-animated progress-bar-success" role="progressbar"
            [ngStyle]="{ 'width': '100%' }"></div>
        </div>
        <label style="text-align: center;">Processing Equipment</label>
      </div>
      <!-- the stuff -->
      <div *ngIf="!updateingEquipment">
        <!-- step title -->
        <div class="mb-3" *ngIf="importInfo?.EquipmentValidation?.length">
          <h2 class="page-title fs-4">Equipment Validation</h2>
          <!--instructions -->
          <div class="mb-3">
            We discovered some changes with the equipment below. You can edit these equipment if you want.
          </div>
          @for (equipment of importInfo?.EquipmentValidation; track $index) {
          <ul class="list-group">
            <li class="list-group-item">
              <div class="row">
                <div class="col-9">
                  <div class="fw-bold">{{equipment.Equipment.Make}} - {{ equipment.Equipment.Model }}</div>
                  @for (cInfo of equipment.Validation; track $index) {
                  @for (info of cInfo.ChangeInfo; track $index) {
                  <div class="text-muted">
                    {{ info }}
                  </div>
                  }
                  }

                  @if(equipment.IsVerify){
                  <div class="mt-3">
                    <div class="form-floating mb-3">
                      <input class="form-control" type="text" [(ngModel)]="equipment.Equipment.Number" />
                      <label for="floatingInput">Number</label>
                    </div>
                    <div class="form-floating mb-3">
                      <input class="form-control" [(ngModel)]="equipment.Equipment.Make" />
                      <label for="floatingInput">Make</label>
                    </div>
                    <div class="form-floating mb-3">
                      <input class="form-control" [(ngModel)]="equipment.Equipment.Model" />
                      <label for="floatingInput">Model</label>
                    </div>
                    <div class="form-floating mb-3">
                      <textarea class="form-control" [(ngModel)]="equipment.Equipment.Notes"></textarea>
                      <label for="floatingInput">Notes</label>
                    </div>
                    <div class="form-floating mb-3">
                      <div class="form-check">
                        <input type="checkbox" name="isactive" id="isactive" class="form-check-input"
                          [(ngModel)]="equipment.Equipment.IsActive" />
                        <label for="isactive" class="form-check-label">Active?</label>
                      </div>
                    </div>
                  </div>
                  }
                </div>
                <div class="col-3 d-flex justify-content-end">
                  <div>
                    <button class="btn btn-outline-secondary" (click)="fixData(equipment)">Edit</button>
                  </div>
                </div>
              </div>
            </li>
          </ul>
          }

        </div>
        <!-- save button -->
        <div class="d-flex justify-content-end"
          *ngIf="importInfo?.EquipmentValidation?.length || importInfo?.NewEquipments?.length">
          <button type="button" class="btn btn-primary" (click)="save()">Finish Import and Save
            Changes</button>
        </div>
      </div>
    </div>
    }
    }
    }
    <!--form-->
    <!-- buttons -->
    <div class="d-flex justify-content-end">
      @if(currentStep > 0 && currentStep < 4){ <div class="btn-group">
        <button type="button" class="btn btn-primary" (click)="goToPreviousStep()"
          [disabled]="currentStep === minStep">Previous Step</button>
        <button type="button" class="btn btn-primary" (click)="goToNextStep()" [disabled]="currentStep === maxStep">Next
          Step</button>
    </div>
    }
</div>
</section>
</div>