import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { toSignal, toObservable } from '@angular/core/rxjs-interop';
import { tap, switchMap } from 'rxjs';
import { ConstructionDailyLogService } from 'src/app/construction/shared/data-access/daily-log-user-project.service';
import { ProjectComponentIdentifiers } from 'src/app/construction/shared/interfaces/project-components';
import { DailyLogProjectComponent, DailyLogUserProjectComponent } from 'src/app/construction/shared/interfaces/daily-log-user-project';
import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';

@Component({
    selector: 'app-diary',
    imports: [CommonModule, CdkDropList, CdkDrag],
    templateUrl: './diary.component.html',
    styleUrl: './diary.component.css'
})
export class ProjectGlobalSettingDiaryComponent {


  isLoading = signal<boolean>(false);

  dailyLogComponentService = inject(ConstructionDailyLogService);
  dailyLogComponent = this.dailyLogComponentService.dailyLogUserComponent;
  componentIdentifier = signal<ProjectComponentIdentifiers>(ProjectComponentIdentifiers.DAILY_LOG);

  private getComponent = toSignal(toObservable(this.componentIdentifier).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap((identifier) => this.dailyLogComponentService.getUserStoreDailyLogComponent()),
    tap((identifier) => {
      this.isLoading.set(false);
    })
  ));
  
  updateComponent(component: DailyLogProjectComponent) { 
    var dlComponent = this.dailyLogComponent() as DailyLogUserProjectComponent;
    component.IsActive = !component.IsActive;
    this.dailyLogComponentService.updateDailyLogComponent(dlComponent).subscribe();
  }

  dropDiary(event: CdkDragDrop<DailyLogProjectComponent[]>) {
    var dlComponent = this.dailyLogComponent() as DailyLogUserProjectComponent;
    moveItemInArray(dlComponent.Components, event.previousIndex, event.currentIndex);

    if(event.previousIndex !== event.currentIndex){
      this.dailyLogComponentService.updateDailyLogComponent(dlComponent).subscribe();
    }
    
  }
  removeComponent(_t8: any) {
    throw new Error('Method not implemented.');
  }
}
