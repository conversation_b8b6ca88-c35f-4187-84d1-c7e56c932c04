import { Component, OnInit, inject, signal, effect } from '@angular/core';
import { environment } from 'src/environments/environment';
import { newGuid } from 'src/app/models/utilities';
import { Observable } from 'rxjs';

import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { ConfirmService } from 'src/app/shared/data-access/confirm.service';
import { LambdaAWSService } from 'src/app/shared/data-access/lambdaaws.service';
import { EquipmentService, EquipmentStoreStatus } from 'src/app/construction/shared/data-access/equipment.service';
import { EquipmentInfo } from 'src/app/construction/shared/interfaces/equipment';
import { CommonModule } from '@angular/common';
import { CivCastAWSUploader } from 'src/app/shared/ui/uploader/civcast.aws.uploader.component';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { EquipmentImportService } from 'src/app/construction/shared/data-access/equipment-import.service';
import { EquipmentImportSettings, EquipmentImportResponse, EquipmentImportRequest, EquipmentGetherInfo } from 'src/app/construction/shared/interfaces/equipment-import';

@Component({
    selector: 'equipment-import',
    standalone: true,
    imports: [CommonModule, CivCastAWSUploader, FormsModule, ReactiveFormsModule],
    templateUrl: './equipment-import-component.html'
})
export class EquipmentImportComponent implements OnInit {
    
    equipmentService = inject(EquipmentService);
    equipmentImportService = inject(EquipmentImportService);
    router = inject(Router);
    aRoute = inject(ActivatedRoute);
    lambdaAwsService = inject(LambdaAWSService);
    confirmService = inject(ConfirmService);
    toastr = inject(ToastrService);
    isLoading: boolean = false;
    hasSettings: boolean = false;
    importKey: string = "";
    currentStep: number = 0;
    maxStep = 5;
    minStep = 1;
    saveSettings: boolean = false;
    equipmentImportSettings: EquipmentImportSettings | null = null;
    equipmentImportResponse: EquipmentImportResponse = {} as EquipmentImportResponse;
    equipmentImportRequest: EquipmentImportRequest = {} as EquipmentImportRequest;
    sampleEquipment: EquipmentInfo = {} as EquipmentInfo;
    importInfo: EquipmentGetherInfo = {} as EquipmentGetherInfo;
    updateingEquipment: boolean = false;
    letterArray: string[] = ['A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z'];

    // Use modern rxResource pattern
    equipmentGrid = this.equipmentService.equipmentGrid;
    isEquipmentLoading = this.equipmentService.isEquipmentLoading;
    
    // Navigation flag for save completion
    private shouldNavigateAfterSave = signal(false);
      
    constructor() {
        // Effect to handle navigation after save completion
        effect(() => {
            const isSaving = this.equipmentService.isSavingEquipment();
            if (!isSaving && this.shouldNavigateAfterSave()) {
                this.shouldNavigateAfterSave.set(false);
                this.router.navigate(["equipment"], { relativeTo: this.aRoute.parent });
            }
        });
    }

    ngOnInit() {
        // Initialize equipment loading with all status
        this.equipmentService.status.set(EquipmentStoreStatus.All);
        this.equipmentService.loadEquipment();

        // this.equipmentImportService.GetSettings().subscribe({
        //     next: (result) => {
        //         this.equipmentImportSettings = result;
        //     }
        // });
    }
    setSelectedFiles(files: FileList) {

        this.isLoading = true;
        let file = files[0];

        this.setFileKey(file).subscribe({
            next: (key) => {
                this.setupImportResponse(key).subscribe({
                    next: (step) => {
                        this.currentStep = step;
                        this.isLoading = false;
                    },
                    error: (err) => {
                        console.log(err);
                        this.isLoading = false;
                    }
                });
            },
            error: (err) => {
                console.log(err);
            }
        });            

    }

    setupImportResponse(key:string): Observable<number> {
        return new Observable<number>(obs => {
            this.equipmentImportRequest = {} as EquipmentImportRequest;
            this.equipmentImportRequest.HasHeader = true;
            
            const equipmentGrid = this.equipmentGrid();
            if (equipmentGrid) {
                this.equipmentImportRequest.CurrentEquipment = equipmentGrid.Equipments;
            }

            if (this.equipmentImportSettings) {
                if (this.equipmentImportSettings.Number && this.equipmentImportSettings.Make && this.equipmentImportSettings.Model && this.equipmentImportSettings.Notes) {
                    this.equipmentImportRequest.Number = this.equipmentImportSettings.Number;
                    this.equipmentImportRequest.Make = this.equipmentImportSettings.Make;
                    this.equipmentImportRequest.Model = this.equipmentImportSettings.Model;
                    this.equipmentImportRequest.Notes = this.equipmentImportSettings.Notes;
    
                    this.hasSettings = true;
                }
            }
    
            if (!this.hasSettings) {
                this.equipmentImportRequest.Number = {
                    Cell: "A",
                    Name: ""
                };
    
                this.equipmentImportRequest.Make = {
                    Cell: "B",
                    Name: ""
                };
    
                this.equipmentImportRequest.Model = {
                    Cell: "C",
                    Name: ""
                };
    
                this.equipmentImportRequest.Notes = {
                    Cell: "D",
                    Name: ""
                };
    
                this.equipmentImportService.GetImportedData(this.importKey, environment.EquipmentImport.Bucket, this.equipmentImportRequest).subscribe({
                    next: (equipmentImportResponse: EquipmentImportResponse) => {
                        this.equipmentImportResponse = equipmentImportResponse;
    
                        if (this.equipmentImportResponse.IsPartial) {
                            this.currentStep = 1;
                        } else {
                            this.gatherEquipmentInfo().subscribe({
                                next: (importInfo) => {
                                    this.importInfo = importInfo;
                                    this.currentStep = 4
                                    obs.next(4);
                                    obs.complete();
                                },
                                error: (err) => {
                                    console.log(err);
                                    obs.error(err);
                                }
                            });                            
                        }
                    },
                    error: (err: any) => {
                        console.log(err);
                        obs.error(err);
                    }
                });
    
        
            } else {
                this.gatherEquipmentInfo().subscribe({
                    next: (importInfo) => {
                        this.importInfo = importInfo;
                        this.currentStep = 4
                        obs.next(4);
                        obs.complete();
                    },
                    error: (err) => {
                        console.log(err);
                        obs.error(err);
                    }
                });
  
            }
        });



    }

    setFileKey(file: File) : Observable<string> {
        return new Observable<string>(obs => {
            var key = `equipment-imports/${newGuid()}`;
            const guid = newGuid()

            this.lambdaAwsService.uploadFilePresignedUrl(key, file).subscribe({
                next: (result: any) => {
                    this.importKey = key;
                    obs.next(key);
                    obs.complete();
                },
                error: (err: any) => {
                    obs.error(err);
                }
            });
        });
    }

    goToNextStep() {

        this.isLoading = true;
        if (this.currentStep == this.maxStep) {
            console.error("max step reached");
        } else {
            this.currentStep++;
        }

        if (this.currentStep == 2) {
            this.getSampleEquipment().subscribe({
                next: (sampleEquipment) => {
                    this.sampleEquipment = sampleEquipment;
                    this.isLoading = false;
                },
                error: (err) => {
                    this.isLoading = false;
                }
            })
        } else if (this.currentStep == 3) {
        } else if (this.currentStep == 4) {
            this.gatherEquipmentInfo().subscribe({
                next: (importInfo) => {
                    this.importInfo = importInfo;
                    this.isLoading = false;
                    // this.router.navigate(["equipment"], { relativeTo: this.aRoute.parent?.parent });
                },
                error: (err) => {
                    this.isLoading = false;
                }
            });
        }
    

    }

    fixData(equipmentInfo: any) {
        if (equipmentInfo.IsVerify) {
            equipmentInfo.IsVerify = !equipmentInfo.IsVerify;
        } else {
            equipmentInfo.IsVerify = true;
        }
    }

    goToPreviousStep() {
        if (this.currentStep == this.minStep) {
        } else {
            this.currentStep--;
        }
    }

    deleteSettings() {
        this.confirmService.open("Are you sure you want to delete your equipment settings?").result.then(async value => {
            this.isLoading = true;

            this.equipmentImportService.DeleteSettings().subscribe({
                next: (result: any) => {
                    this.toastr.success("Settings have been removed");
                    this.equipmentImportSettings = null; 
                    this.isLoading = false;
                },
                error: (err: any) => {
                    console.log(err);
                    this.isLoading = false;
                }
            });      
        });

    }

    saveImportSettings(): Observable<EquipmentImportSettings | null> {
        return new Observable<EquipmentImportSettings | null>(obs => {
            let importSettings = {} as EquipmentImportSettings;

            if(this.equipmentImportRequest){
                importSettings.Number = this.equipmentImportRequest.Number;
                importSettings.Make = this.equipmentImportRequest.Make;
                importSettings.Model = this.equipmentImportRequest.Model;
                importSettings.Notes = this.equipmentImportRequest.Notes;
                importSettings.WorksheetName = (this.equipmentImportRequest.WorksheetName)? this.equipmentImportRequest.WorksheetName : "";
                importSettings.HasHeader = this.equipmentImportRequest.HasHeader;
        
                this.equipmentImportService.SaveSettings(importSettings).subscribe({
                    next: (result: any) => {
                        obs.next(result);
                        obs.complete();
                    },
                    error: (err) => {
                        obs.error(err);
                    }
                });
            }else{
                obs.next(null);
                obs.complete();
            }


        });


    }

    gatherEquipmentInfo() : Observable<EquipmentGetherInfo> {        
        return new Observable<EquipmentGetherInfo>(obs => {
            if (this.saveSettings) {
                this.saveImportSettings().subscribe();
            }
    
            if(this.equipmentImportRequest){
                this.equipmentImportService.GatherEquipment(this.importKey, environment.EquipmentImport.Bucket, this.equipmentImportRequest).subscribe({
                    next: (importInfo) => {
                        this.importInfo = importInfo;
                        if (importInfo?.EquipmentValidation && importInfo?.NewEquipments) {
                            if (importInfo.EquipmentValidation.length === 0 && importInfo.NewEquipments.length > 0) {
                                this.saveEquipment(importInfo);
                                // Equipment data will be refreshed automatically via rxResource
                                obs.next(importInfo);
                                obs.complete();
                            }else{
                                obs.next(importInfo);
                                obs.complete();
                            }
                        }else{
                            obs.error("Equipment Import missing")
                        }
                    },
                    error: (err) => {
                        obs.error(err);
                    }
                })
            }
        });


    }

    save(){
        this.shouldNavigateAfterSave.set(true);
        this.saveEquipment(this.importInfo);
    }

    saveEquipment(importInfo: any): void {
        let eqList = new Array<EquipmentInfo>();

        if(importInfo){
            for (let eq of importInfo.EquipmentValidation) {
                eqList.push(eq.Equipment);
            }

            // Add new equipment from NewEquipments array if available
            if (importInfo.NewEquipments) {
                eqList.push(...importInfo.NewEquipments);
            }
        }

        this.equipmentService.ImportData(eqList);
    }

    getSampleEquipment() : Observable<EquipmentInfo> {                  
        return new Observable<EquipmentInfo>(obs => {
            this.equipmentImportService.GetSampleEquipment(this.importKey, environment.EquipmentImport.Bucket, this.equipmentImportRequest).subscribe({
                next: (sampleEquipment: EquipmentInfo) => {
                    this.sampleEquipment = sampleEquipment;
                    obs.next(sampleEquipment);
                    obs.complete();
                },
                error: (err: any) => {
                    console.log(err);
                    this.toastr.error("Equipment Import missing");
                }
            });
        });
    }

    error(evt:any){
        
    }

}