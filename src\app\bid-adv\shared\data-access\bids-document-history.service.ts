import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { BidOpsDocument, DocumentDownloadHistory } from '../interfaces/bids-download-history';
@Injectable()
export class BidDocumentsHistoryService {

  constructor(private client: HttpClient) {}

  getDocumentHistory(projectId:string, max:number = 25, currentPage: number = 0, orderBy: string, isReversed: boolean, search: string | null = null): Observable<DocumentHistoryGrid>{
    var url = `${environment.services_root_endpoints.bid_ops_document_history}/docs/history/${projectId}`;
    

    url += `?max=${max}&current-page=${currentPage}`;

    if(search){
      url += `&search=${search}`;
    }

    if(orderBy){
      url +=`&order-by=${orderBy}`;
    }

    url +=`&is-reversed=${isReversed}`;

	  return this.client.get<DocumentHistoryGrid>(url);    
  }

  getDocuments(projectId: string): Observable<Array<BidOpsDocument>>{
	return this.client.get<Array<BidOpsDocument>>(`${environment.services_root_endpoints.bid_ops_documents}/docs/${projectId}`);   
  }
}

export interface DocumentHistoryGrid{
  DocumentDownloadHistorys: Array<DocumentDownloadHistory>;
  TotalCount: number;
}