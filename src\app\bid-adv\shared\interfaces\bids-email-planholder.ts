export interface MessageCenterEmailLog{
	Id: string | null;
	CreatedDate: string | null;
	SentDate: string | null;
	GatheredEmails:Array<EmailAddress>;
	Subject:string | null;
	Body:string | null;
}

export interface EmailPlanholderRequest{

}

export interface EmailPlanholderResponse{

}

export interface EmailAddress{
	UserId:string;
	Address:string;
	FirstName: string;
	LastName:string;
	CompanyName:string;
	DisplayName:string;
}