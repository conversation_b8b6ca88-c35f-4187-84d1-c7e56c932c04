<!-- page header -->
<header class="bg-light p-3 p-lg-4">
  <div class="container">
    <!-- page title -->
    <div class="d-flex justify-content-between">
      <h1 class="page-title fs-5">{{ role?.Name }}</h1>
      <button class="btn btn-primary" (click)="saveRole()" [disabled]="isSaving">
        <i class="fas fa-circle-notch fa-spin fa-1x mx-2" *ngIf="isSaving"></i>
        Save
      </button>
    </div>
    <p class="mb-0">Manage the permissions on this role.</p>
  </div>
</header>
<!-- permissions -->
<section class="p-3 p-lg-4">
  <div class="container">
    @if(isPermissionsLoading){
    <app-list-skeleton></app-list-skeleton>
    }@else {
    <access-permissions (add-policy)="addPolicy($event)" (remove-policy)="removePolicy($event)"
      (selected-policy)="selectedPolicy($event)" [policies]="policies">
    </access-permissions>
    }
  </div>
</section>