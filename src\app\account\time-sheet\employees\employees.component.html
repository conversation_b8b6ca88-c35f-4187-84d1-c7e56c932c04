<!-- page header -->
<header class="bg-light p-3 p-lg-4">
	<div class="container">
		<!-- page title -->
		<h1 class="page-title fs-5 mb-3">Employees</h1>
		<p>Add employees so foremen can record employee hours on the time sheet in the diary.</p>
		<div class="row">
			<div class="col-12 col-lg-6 mb-2 mb-lg-0">
				<div class="input-group me-2">
					<span class="input-group-text" id="basic-addon1">
						<i class="fa fa-search" aria-hidden="true"></i>
					</span>
					<input type="text" class="form-control" placeholder="{{ inputPlaceholder }}"
						aria-describedby="basic-addon1" [formControl]="search" name="search" />
				</div>
			</div>
			<div class="col-12 col-lg-3 mb-2 mb-lg-0">
				<div class="btn-group">
					<button type="button" class="btn btn-outline-dark" (click)="gatherAll('active')"
						[ngClass]="{ active: status === 'active' }">Active</button>
					<button type="button" class="btn btn-outline-dark" (click)="gatherAll('nonactive')"
						[ngClass]="{ active: status === 'nonactive' }">
						Inactive
					</button>
					<button type="button" class="btn btn-outline-dark" (click)="gatherAll('all')"
						[ngClass]="{ active: status === 'all' }">All</button>
				</div>
			</div>
			<div class="col-12 col-lg-3 mb-2 mb-lg-0 d-lg-flex justify-content-lg-end">
				<div class="dropdown">
					<button class="btn btn-outline-dark dropdown-toggle" type="button" id="dropdownMenuButton1"
						data-bs-toggle="dropdown" aria-expanded="false">
						Add Employees
					</button>
					<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
						<li> <a class="dropdown-item" href="javascript:void(0)" (click)="addEmployee()">Add Employee</a>
						</li>
						<li> <a class="dropdown-item" href="javascript:void(0)" (click)="importEmployees()">Import</a>
						</li>
						<li>
							<hr class="dropdown-divider">
						</li>
						<li><a class="dropdown-item" [href]="templateUrl" target="_self">Download Template</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
</header>
<!-- employees -->
<section class="p-3 p-lg-4">
	<div class="container">
		@if(loading()){
		<ul class="list-group">
			<li class="list-group-item bg-light" style="height: 50px">
			</li>
			<li class="list-group-item" *ngFor="let item of [1,2,3,4,5,6,7]">
				<div class="placeholder-glow">
					<div class="row">
						<div class="col-12">
							<span class="placeholder col-12" style="height:20px;"></span>
						</div>
					</div>
					<div class="row">
						<div class="col-4">
							<span class="placeholder col-12"></span>
						</div>
					</div>
				</div>
			</li>
		</ul>
		}@else{
		@if(view === 'list'){
		<ul class="list-group">
			@for (employee of employees; track $index) {
			<li class="list-group-item d-flex align-items-center justify-content-between">
				<div>
					<p class="fw-bold mb-1">{{ employee.FirstName }} {{ employee.LastName }}</p>
					<p class="text-secondary small mb-1">{{ employee.Classification }}</p>
					<p class="text-secondary small mb-1">{{ employee.CustomId }}</p>
					@if(!employee.IsActive && status !== 'nonactive'){
					<div class="text-secondary small"><span class="badge bg-danger">Inactive</span></div>
					}
				</div>
				<!--*ngIf="updateEmployeeAccess === 'Allow'"-->
				<div>
					<div class="dropdown">
						<button class="btn btn-outline-dark dropdown-toggle" type="button" id="dropdownMenuButton1"
							data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fa fa-pencil" aria-hidden="true"></i>
						</button>
						<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
							<li>
								<a class="dropdown-item" href="javascript:void(0)"
									(click)="editEmployee(employee)">Edit</a>
							</li>

							@if((status === 'active' || status === 'all') && employee.IsActive){
							<li>
								<a class="dropdown-item" href="javascript:void(0)"
									(click)="deactivate($index, employee)">Deactivate</a>
							</li>
							}

							@if(!employee.IsActive){
							<li>
								<a class="dropdown-item" href="javascript:void(0)"
									(click)="activate($index, employee)">Activate</a>
							</li>
							}
						</ul>
					</div>
				</div>
			</li>
			}@empty {
			<li class="list-group-item">
				<div class="alert alert-info m-0" role="alert">
					No employees.
				</div>
			</li>
			}
		</ul>
		}
		}
	</div>
</section>