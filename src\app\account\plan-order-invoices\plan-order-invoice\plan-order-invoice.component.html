<!-- Invoice Header -->
<div class="container p-4 mb-4">
  <!-- header -->
  <div class="d-flex justify-content-between align-items-center mb-3">
    <h1 class="fs-5 mb-0">Order Invoice</h1>
  </div>
  
  <!-- Loading State -->
  @if (isLoading()) {
    <app-invoice-skeleton></app-invoice-skeleton>
  } @else if (error()) {
    <!-- Error State -->
    <div class="card p-3 mb-3">
      <div class="alert alert-danger text-center">
        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
        <h4 class="alert-heading">Error Loading Invoice</h4>
        <p class="mb-0">{{ error() }}</p>
      </div>
    </div>
  } @else if (receiptData()) {
    <!-- Invoice Content -->
    <section>
    <!-- Order Summary Card -->
    <div class="card p-3 mb-3">
      <div class="card-header">
        <h5 class="mb-0">Order Summary</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <p><strong>PO Number:</strong> {{ receiptData()!.PoNumber }}</p>
            <p><strong>Order Date:</strong> {{ formatDate(receiptData()!.OrderDate!) }}</p>
          </div>
          <div class="col-md-6">
            <p><strong>Invoice ID:</strong> {{ receiptData()!.Id }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Customer Information -->
    <div class="card p-3 mb-3">
      <div class="card-header">
        <h5 class="mb-0">Customer Information</h5>
      </div>
      <div class="card-body">
        <div class="row">
          <div class="col-md-6">
            <h6>Contact Details</h6>
            <p class="mb-1">{{ receiptData()!.Name }}</p>
            <p class="mb-1">{{ receiptData()!.Email }}</p>
            <p class="mb-0">{{ receiptData()!.Phone }}</p>
          </div>
          <div class="col-md-6">
            <h6>Shipping Address</h6>
            <p class="mb-1">{{ receiptData()!.ShippingAddress.Name }}</p>
            @if (receiptData()!.ShippingAddress.Company) {
              <p class="mb-1">{{ receiptData()!.ShippingAddress.Company }}</p>
            }
            <p class="mb-1">{{ receiptData()!.ShippingAddress.Address1 }}</p>
            @if (receiptData()!.ShippingAddress.Address2) {
              <p class="mb-1">{{ receiptData()!.ShippingAddress.Address2 }}</p>
            }
            <p class="mb-0">
              {{ receiptData()!.ShippingAddress.City }}, 
              {{ receiptData()!.ShippingAddress.State }} 
              {{ receiptData()!.ShippingAddress.ZipCode }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Shipping Information -->
    @if (receiptData()!.Carrier) {
      <div class="card p-3 mb-3">
        <div class="card-header">
          <h5 class="mb-0">Shipping Information</h5>
        </div>
        <div class="card-body">
          <p><strong>Carrier:</strong> {{ receiptData()!.Carrier }}</p>
        </div>
      </div>
    }

    <!-- Order Items -->
    <div class="card p-3 mb-3">
      <div class="card-header">
        <h5 class="mb-0">Order Items</h5>
      </div>
      <div class="card-body p-0">
        <div class="table-responsive">
          <table class="table table-striped mb-0">
            <thead>
              <tr>
                <th>Item</th>
                <th>Size</th>
                <th class="text-center">Quantity</th>
                <th class="text-end">Price Per Page</th>
                <th class="text-end">Total</th>
              </tr>
            </thead>
            <tbody>
              @for (item of receiptData()!.Items; track $index) {
                <tr>
                  <td>{{ item.Title }}</td>
                  <td>{{ item.SelectedSize }}</td>
                  <td class="text-center">{{ item.SelectedQuantity }}</td>
                  <td class="text-end">${{ item.PricePerPage?.toFixed(2) }}</td>
                  <td class="text-end">${{ item.Total?.toFixed(2) }}</td>
                </tr>
              }
            </tbody>
          </table>
        </div>
      </div>
    </div>

    <!-- Order Totals -->
    <div class="card p-3 mb-3">
      <div class="card-body">
        <div class="row justify-content-end">
          <div class="col-md-4">
            <div class="d-flex justify-content-between mb-2">
              <span>Subtotal:</span>
              <span>${{ receiptData()!.Subtotal?.toFixed(2) }}</span>
            </div>
            @if (receiptData()?.Tax && receiptData()!.Tax > 0) {
              <div class="d-flex justify-content-between mb-2">
                <span>Tax:</span>
                <span>${{ receiptData()!.TaxAmount.toFixed(2) }}</span>
              </div>
            }
            @if (receiptData()?.Shipping && receiptData()!.Shipping > 0) {
              <div class="d-flex justify-content-between mb-2">
                <span>Shipping:</span>
                <span>${{ receiptData()!.Shipping.toFixed(2) }}</span>
              </div>
            }
            <hr>
            <div class="d-flex justify-content-between">
              <strong>Total:</strong>
              <strong>${{ receiptData()!.Total?.toFixed(2) }}</strong>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <div class="text-center mt-4 mb-4">
      <p class="text-muted">
        <small>
          Thank you for your order! If you have any questions, please contact customer support.
        </small>
      </p>
    </div>
    </section>
  }
</div>