import { Component, inject, effect, OnInit, OnDestroy, computed } from '@angular/core';
import { CommonModule } from '@angular/common';
import { debounceTime, distinctUntilChanged, Subject, takeUntil } from 'rxjs';
import { environment } from 'src/environments/environment';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { AccessEffects } from 'src/app/shared/interfaces/access';
import { EquipmentInfo, EquipmentStore } from 'src/app/construction/shared/interfaces/equipment';
import { EquipmentService, EquipmentStoreStatus } from 'src/app/construction/shared/data-access/equipment.service';

@Component({
    selector: 'app-equipment',
    imports: [CommonModule, FormsModule, ReactiveFormsModule, NgbPaginationModule],
    templateUrl: './equipment.component.html',
    styleUrl: './equipment.component.css'
})
export class EquipmentComponent implements OnInit, OnDestroy {
  router = inject(Router);
  route = inject(ActivatedRoute);
  equipmentService = inject(EquipmentService);
  toastr = inject(ToastrService);
  templateUrl: string = "";
  search = new UntypedFormControl();

  // Search input handling with debouncing (following bids-filter pattern)
  searchInputDelay = 600;
  searchInputChange = new Subject<string>();
  private destroy$ = new Subject<void>();

  // Direct signal references following bids.component.ts pattern
  equipmentGrid = this.equipmentService.equipmentGrid;
  permissions = this.equipmentService.equipmentPermissions;
  status = this.equipmentService.status;
  currentPage = this.equipmentService.currentPage;
  limit = this.equipmentService.limit;
  searchSignal = this.equipmentService.search;

  // Computed signal to prevent flickering during background refreshes
  // Only show loading skeleton on initial load, not during debounced reloads
  isLoading = computed(() => {
    const loading = this.equipmentService.isEquipmentLoading();
    const hasData = (this.equipmentGrid()?.Equipments?.length ?? 0) > 0;
    // Show loading only if we're loading AND don't have any data yet
    return loading && !hasData;
  });

  public readonly ACCESS_EFFECTS: typeof AccessEffects = AccessEffects;
  public readonly EQUIPMENT_STATUS: typeof EquipmentStoreStatus = EquipmentStoreStatus;
  
  // Track item-specific loading states for granular UI feedback (delete/status buttons) 
  // while global isSavingEquipment handles page-level state. Cleared on global completion.
  private itemsWithLoadingState = new Map<string, EquipmentInfo>();

  ngOnInit(): void {
    // Setup URL synchronization following bids-filter.component.ts pattern
    this.route.queryParams.subscribe(params => {
      if (params['page']) {
        this.equipmentService.currentPage.set(parseInt(params['page']));
      } else {
        this.equipmentService.currentPage.set(1);
      }

      if (params['limit']) {
        this.equipmentService.limit.set(parseInt(params['limit']));
      } else {
        this.equipmentService.limit.set(100);
      }

      if (params['status']) {
        this.equipmentService.status.set(params['status'] as EquipmentStoreStatus);
      } else {
        this.equipmentService.status.set(EquipmentStoreStatus.Active);
      }

      if (params['search']) {
        this.equipmentService.search.set(params['search']);
        this.search.setValue(params['search'], { emitEvent: false });
      } else {
        this.equipmentService.search.set(null);
        this.search.setValue('', { emitEvent: false });
      }

      // Initialize equipment service after URL params are processed
      this.equipmentService.loadEquipment();
    });

    // Load permissions
    this.equipmentService.getEquipmentPermissions();
  }

  constructor() {
    this.templateUrl = environment.excel_template_locations.equipment;
    this.setupSearchInputs();

    // Effect to reset all item loading states when any equipment operation completes.
    // Since service operations are atomic (one at a time), when global isSavingEquipment
    // becomes false, all pending item operations are done and can be reset for clean UI state.
    effect(() => {
      const isSaving = this.equipmentService.isSavingEquipment();
      if (!isSaving) {
        // Reset all local loading states when service operation completes
        this.itemsWithLoadingState.forEach(item => {
          item.isLoading = false;
        });
        this.itemsWithLoadingState.clear();
      }
    });

    // Effect to handle automatic page navigation when current page becomes empty
    // This handles the case where all items on the last page are deactivated/removed
    effect(() => {
      const equipmentStore = this.equipmentGrid();
      const currentPage = this.currentPage();
      const limit = this.limit();

      if (equipmentStore && currentPage > 1) {
        const hasItems = equipmentStore.Equipments && equipmentStore.Equipments.length > 0;
        const totalPages = Math.ceil(equipmentStore.Total / limit);

        // If current page is empty and we're not on page 1, navigate to previous page
        if (!hasItems && currentPage > totalPages) {
          const newPage = Math.max(1, currentPage - 1);
          this.router.navigate([], {
            queryParams: { page: newPage },
            queryParamsHandling: "merge"
          });
        }
      }
    });
  }

  // Setup search input handling with debouncing (following bids-filter pattern)
  setupSearchInputs(): void {
    this.searchInputChange
      .pipe(
        debounceTime(this.searchInputDelay),
        distinctUntilChanged(),
        takeUntil(this.destroy$)
      )
      .subscribe(value => {
        let search = null;
        if (value && value.trim() !== '') {
          search = value.trim();
        }
        // Update the service's search signal and URL
        this.equipmentService.search.set(search);
        this.router.navigate([], {
          queryParams: { search: search || null, page: 1 },
          queryParamsHandling: "merge"
        });
      });
  }

  // Status filter change following bids.component.ts pattern
  gatherAll(status: EquipmentStoreStatus) {
    this.equipmentService.status.set(status);
    this.router.navigate([], {
      queryParams: { status: status, page: 1 },
      queryParamsHandling: "merge"
    });
  }

  // Pagination change following bids.component.ts pattern
  changePage(pageInfo: number) {
    this.router.navigate([], {
      queryParams: { page: pageInfo },
      queryParamsHandling: "merge"
    });
  }

  changeStatus(equipment: EquipmentInfo) {
      equipment.isLoading = true;
      this.itemsWithLoadingState.set(equipment.InternalId, equipment);
      this.equipmentService.ChangeEquipmentStatus(equipment.InternalId, !equipment.IsActive);
  }

  importEquipment() {
      this.router.navigate(["import"], {relativeTo: this.route});
  }

  addEquipmentItem() {
    this.router.navigate(["add"], {relativeTo: this.route});
  }

  editItem(item: EquipmentInfo) {
    this.router.navigate(["edit", item.InternalId], {relativeTo: this.route});
  }

  deleteItem(item: EquipmentInfo) {
      item.isLoading = true;
      this.itemsWithLoadingState.set(item.InternalId, item);
      this.equipmentService.RemoveEquipmentItem(item);
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
    this.itemsWithLoadingState.clear();
  }
}