<div class="container p-4 mb-4">
    <!-- header -->
    <h1 class="fs-5 mb-3">New Project Settings</h1>
    <!-- new project settings -->
    <section class="mb-3">
        <!-- main form -->
        <div [formGroup]="settingsForm">
            <!-- import contact information -->
            <div class="card mb-4">
                <div class="card-body">
                    <h2 class="card-title fs-6">Import Contact Information</h2>
                    <div class="form-check">
                        @if (isLoading) {
                        <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                        } @else {
                        <input type="checkbox" class="form-check-input" id="newProjectImportContactInfo"
                            formControlName="newProjectImportContactInfo" />
                        <label class="form-check-label" for="newProjectImportContactInfo">
                            Automatically populate my contact information when listing a new project.
                        </label>
                        }
                    </div>
                </div>
            </div>
            <!-- q&a contacts -->
            <div class="card">
                <div class="card-body">
                    <h2 class="card-title fs-6">Q&A Contacts</h2>
                    <p>Automatically add the emails below to the Q&A alerts section so these recipients are notified whenever a new question is submitted on a project.
                    </p>
                    <!-- Add New Contact -->
                    <div class="row mb-3" [formGroup]="newContactForm">
                        <div class="col-12 col-md-4 col-lg-4 mb-3 mb-md-0">
                            <input type="text" class="form-control" placeholder="Name" formControlName="contactName" />
                        </div>
                        <div class="col-12 col-md-4 col-lg-4 mb-3 mb-md-0">
                            <input type="email" class="form-control" placeholder="Email"
                                formControlName="contactEmail" />
                        </div>
                        <div class="col-12 col-md-4 col-lg-4 mb-3 mb-md-0 text-md-">
                            <button type="button" class="btn btn-outline-dark me-2"
                                (click)="addQAContact()">Add</button>
                            <button type="button" class="btn btn-outline-dark"
                                (click)="cancelNewContact()">Cancel</button>
                        </div>
                    </div>
                    <!-- Existing Contacts -->
                    <table class="table">
                        <thead>
                            <tr class="d-none d-lg-table-row">
                                <th scope="col">Name</th>
                                <th scope="col">Email</th>
                                <th scope="col"></th>
                            </tr>
                        </thead>
                        @if (isLoading) {
                        <tbody>
                            <tr>
                                <td class="placeholder-glow">
                                    <span class="placeholder w-100"></span>
                                </td>
                                <td class="placeholder-glow">
                                    <span class="placeholder w-100"></span>
                                </td>
                                <td class="placeholder-glow">
                                    <span class="placeholder w-100"></span>
                                </td>
                            </tr>
                        </tbody>
                        } @else {
                        @if(qaContacts.controls.length === 0){
                        <tbody>
                            <tr>
                                <td colspan="3">
                                    <div class="alert alert-info m-0" role="alert">
                                        No contacts added yet.
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        }
                        @if(qaContacts){
                        <tbody formArrayName="qaContacts">
                            @for(contact of qaContacts.controls; track contact){
                            <tr [formGroupName]="qaContacts.controls.indexOf(contact)">
                                <!-- name -->
                                <td class="d-none d-lg-table-cell align-middle">
                                    @if(editingIndex !== qaContacts.controls.indexOf(contact)){
                                    <ng-container>
                                        <span>{{ contact.value.contactName }}</span>
                                    </ng-container>
                                    } @else {
                                    <input type="text" class="form-control" formControlName="contactName" />
                                    }
                                </td>
                                <!-- email -->
                                <td class="d-none d-lg-table-cell align-middle text-muted">
                                    @if(editingIndex !== qaContacts.controls.indexOf(contact)){
                                    <ng-container>
                                        <span>{{ contact.value.contactEmail }}</span>
                                    </ng-container>
                                    } @else {
                                    <input type="email" class="form-control" formControlName="contactEmail" />
                                    }
                                </td>
                                <!-- buttons -->
                                <td class="d-none d-lg-table-cell align-middle text-lg-end">
                                    @if(editingIndex !== qaContacts.controls.indexOf(contact)){
                                    <button type="button" class="btn btn-outline-dark me-2"
                                        (click)="editQAContact(qaContacts.controls.indexOf(contact))">
                                        Edit
                                    </button>
                                    } @else {
                                    <button type="button" class="btn btn-outline-dark me-2"
                                        (click)="applyQAContact(qaContacts.controls.indexOf(contact))">
                                        Apply
                                    </button>
                                    <button type="button" class="btn btn-outline-dark me-2"
                                        (click)="cancelEditQAContact(qaContacts.controls.indexOf(contact))">
                                        Cancel
                                    </button>
                                    }
                                    <button type="button" class="btn btn-outline-danger"
                                        (click)="removeQAContact(qaContacts.controls.indexOf(contact))">
                                        Remove
                                    </button>
                                </td>
                                <!-- mobile -->
                                <td class="d-lg-none">
                                    <div class="mb-1">
                                        @if(editingIndex !== qaContacts.controls.indexOf(contact)){
                                        <ng-container>
                                            <span class="fw-bold">{{ contact.value.contactName }}</span>
                                        </ng-container>
                                        } @else {
                                        <input type="text" class="form-control" formControlName="contactName" />
                                        }
                                    </div>
                                    <div class="mb-1">
                                        @if(editingIndex !== qaContacts.controls.indexOf(contact)){
                                        <ng-container>
                                            <span>{{ contact.value.contactEmail }}</span>
                                        </ng-container>
                                        } @else {
                                        <input type="email" class="form-control" formControlName="contactEmail" />
                                        }
                                    </div>
                                    <div>
                                        @if(editingIndex !== qaContacts.controls.indexOf(contact)){
                                        <button type="button" class="btn btn-outline-dark me-2"
                                            (click)="editQAContact(qaContacts.controls.indexOf(contact))">
                                            Edit
                                        </button>
                                        } @else {
                                        <button type="button" class="btn btn-outline-dark me-2"
                                            (click)="applyQAContact(qaContacts.controls.indexOf(contact))">
                                            Apply
                                        </button>
                                        <button type="button" class="btn btn-outline-dark me-2"
                                            (click)="cancelEditQAContact(qaContacts.controls.indexOf(contact))">
                                            Cancel
                                        </button>
                                        }
                                        <button type="button" class="btn btn-outline-dark"
                                            (click)="removeQAContact(qaContacts.controls.indexOf(contact))">
                                            Remove
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            }
                        </tbody>
                        }
                        }
                    </table>
                </div>
            </div>
        </div>
    </section>
    <!-- footer -->
    <footer class="d-flex justify-content-end">
        <button type="button" class="btn btn-primary" (click)="saveSettings()" [disabled]="isSaving">
            @if (isSaving) {
            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            } Save
        </button>
    </footer>
</div>