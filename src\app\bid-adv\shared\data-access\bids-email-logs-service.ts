import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable } from 'rxjs';
import { EmailLog } from '../interfaces/bids-email-log';

@Injectable()
export class BidsAdvEmailLogsService {

  constructor(private client: HttpClient) {}

  getEmailLogs(projectId:string, max:number = 25, currentPage: number = 0, orderBy: string, isReversed: boolean, search: string | null = null): Observable<EmailLogGrid>{
    var url = `${environment.services_root_endpoints.bid_ops_emaillogs}/email-logs/${projectId}`;
    

    url += `?max=${max}&current-page=${currentPage}`;

    if(search){
      url += `&search=${search}`;
    }

    if(orderBy){
      url +=`&order-by=${orderBy}`;
    }

    url +=`&is-reversed=${isReversed}`;

	  return this.client.get<EmailLogGrid>(url);    
  }


}

export interface EmailLogGrid{
  EmailLogs: Array<EmailLog>;
  TotalCount: number;
}