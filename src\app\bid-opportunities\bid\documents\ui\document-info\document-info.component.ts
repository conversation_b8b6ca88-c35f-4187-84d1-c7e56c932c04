import { Component, OnDestroy, computed, effect, inject, input, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { GroupByPipe } from 'src/app/shared/utils/pipes/groupBy.pipe';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { Subscription, tap } from 'rxjs';
import { BidOpsDocumentService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-document.service';
import { BidOpsProjectService } from 'src/app/bid-opportunities/shared/data-access/bid-ops-project.service';
import { BidOpsProject } from 'src/app/bid-opportunities/shared/interfaces/bid';
import { ProjectFileInfo } from 'src/app/bid-opportunities/shared/interfaces/bid-ops-documents';
import { FormsModule } from '@angular/forms';
import { DownloadHistoryService } from 'src/app/bid-opportunities/shared/data-access/download-history.service';
import { DocumentSkeletonComponent } from '../document-skeleton/document-skeleton.component';
import { ToastrService } from 'ngx-toastr';
import { AuthService } from 'src/app/shared/data-access/auth.service';

@Component({
    selector: 'app-document-info',
    imports: [CommonModule, FormsModule, GroupByPipe, DocumentSkeletonComponent],
    templateUrl: './document-info.component.html',
    styleUrl: './document-info.component.css'
})
export class DocumentInfoComponent implements OnDestroy {

  /////////// Services //////////
  bidOpsProjectService = inject(BidOpsProjectService);
  bidOpsDocumentService = inject(BidOpsDocumentService);

  toastrService = inject(ToastrService);

  /////////// Signals //////////
  files = this.bidOpsDocumentService.files;
  fileLimitCheck = this.bidOpsDocumentService.zipFileLimitCheck;
  
  isFilesAllowedToDownloadAll = this.bidOpsDocumentService.isFilesAllowedToDownloadAll;
  isSelectedFilesDownloadAll = this.bidOpsDocumentService.isSelectedFilesDownloadAll;
  isDownloadAll = this.bidOpsDocumentService.isDownloadAll;
  allFiles = this.bidOpsDocumentService.allFiles;
  isDownloadHistoryLoading = this.bidOpsDocumentService.isDownloadHistoryLoading;

  /////////// Inputs //////////
  disableView = input.required<boolean>();
  project = input.required<BidOpsProject>();

  /////////// Local Properties //////////
  downloadFileSubscription: Subscription | null = null;
  isLoading = false;
  verifyProjectPlans = false;
  downloadSelectedDisabled = computed(() => this.selectedFiles()?.length <= 0);
  selectedFiles = signal<Array<ProjectFileInfo>>([]);

  constructor() {
    effect(() => {
      if(this.bidOpsProjectService.projectId()){
        this.bidOpsDocumentService.projectId.set(this.bidOpsProjectService.projectId() as string);    
      }
    });
  }


  setupHistory(downloadHistory: Array<string>, files: Array<ProjectFileInfo>) {
    for(let file of files){
      var fileId = downloadHistory.find((fileId) => file.File.FileId == fileId);
      if(fileId){
        file.File.IsDownloaded = true;
      }else{
        file.File.IsDownloaded = false;
      }
    }
  }

  ngOnDestroy(): void {
    this.downloadFileSubscription?.unsubscribe();
  }

  setSelectedDocuments(file: ProjectFileInfo) {
    this.selectedFiles.update((files) => {
      if (files.find((f) => f.File.FileId == file.File.FileId)) {
        files = files.filter((f) => f.File.FileId != file.File.FileId);
      } else {
        files.push(file);
      }
      return [...files];
    });
  }
  downloadAll() {
   this.bidOpsDocumentService.downloadAll();
  }

  downloadSelected() {
    this.bidOpsDocumentService.downloadSelected(this.selectedFiles());
  }

  download(projectFile: ProjectFileInfo) {    
    this.bidOpsDocumentService.download(projectFile);
  }

}