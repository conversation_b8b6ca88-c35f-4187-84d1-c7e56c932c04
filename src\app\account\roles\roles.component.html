<div class="container p-4 mb-4">
  <!-- header -->
  <header>
    <!-- page title -->
    <div class="d-flex justify-content-between">
      <h1 class="fs-5">Roles</h1>
      @if(hasAddRoleAccess === 'Allow'){
      <a class="btn btn-primary text-white" type="button" placement="bottom" ngbTooltip="Add New Role" routerLink="access/add">
        + Add Role
      </a>
      }
    </div>
    <p> Create roles with permissions so you can assign them to users. Drag and drop to arrange the
      order.</p>
  </header>
  <!-- search -->
  <section class="col-12 col-md-6 col-xl-4 mb-3">
    <div class="input-group">
      <span class="input-group-text" id="basic-addon1"><i class="fas fa-search"></i></span>
      <input type="search" name="searchRoles" [formControl]="searchRole" class="form-control"
        placeholder="Search by role name" aria-label="Username" aria-describedby="basic-addon1">
    </div>
  </section>
  <!-- roles -->
  <section>
    @if(isLoading){
    <app-list-skeleton></app-list-skeleton>
    }@else {
    <!--roles -->
    <table class="table align-middle">
      <tbody>
        <!--dnd-sortable [sortableIndex]="i" (dragend)="dragEnd()" TODO: Fix Drag and Drop-->
        @for (role of roles | orderBy: 'Name'; track $index) {
        <!-- mobile -->
        <tr class="d-md-none">
          <td>
            <div class="mb-2">
              <i class="fas fa-grip-vertical fa-lg drag-handle text-secondary me-2" aria-hidden="true"></i>
              {{ role.Name}}
            </div>
            <div>
              <a class="btn btn-outline-dark me-2" routerLink="access/manage/{{ role.Id }}">Permissions</a>
              <button type="button" class="btn btn-outline-dark me-2" (click)="navigateToRoleSettings(role)">
                Settings
              </button>
              @if(hasDeleteRoleAccess === 'Allow'){
              <button type="button" class="btn btn-outline-danger" (click)="removeRole(role)">
                Delete
              </button>
              }
            </div>
          </td>
        </tr>
        <!-- desktop -->
        <tr class="d-none d-md-table-row">
          <td>
            <i class="fas fa-grip-vertical fa-lg drag-handle text-secondary me-3" aria-hidden="true"></i>
            <span>{{ role.Name}}</span>
          </td>
          <td>
            <div class="d-flex justify-content-end">
              <a class="btn btn-outline-dark me-2" routerLink="access/manage/{{ role.Id }}">Permissions</a>
              <button type="button" class="btn btn-outline-dark me-2" (click)="navigateToRoleSettings(role)">
                Settings
              </button>
              @if(hasDeleteRoleAccess === 'Allow'){
              <button type="button" class="btn btn-outline-danger" (click)="removeRole(role)">
                Delete
              </button>
              }
            </div>
          </td>
        </tr>
        }
      </tbody>
    </table>
    }
  </section>
</div>