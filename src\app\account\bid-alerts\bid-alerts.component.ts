import { Component, OnInit, inject } from '@angular/core';
import { Form<PERSON>uilder, FormGroup, FormArray, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';
import { BidNotificationSettingsService } from 'src/app/account/shared/data-access/bid-notification-settings.service';
import { BidNotificationSetting, StateCounty } from 'src/app/account/shared/interfaces/bid-notification-settings.model';
import { HttpClient } from '@angular/common/http';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';

const DEFAULT_STATE = 'Texas';

@Component({
    selector: 'app-bid-alerts',
    standalone: true,
    imports: [CommonModule, ReactiveFormsModule],
    templateUrl: './bid-alerts.component.html',
    styleUrls: ['./bid-alerts.component.css']
})
export class BidAlertsComponent implements OnInit {
    settingsForm!: FormGroup;
    states: { Name: string, Abbreviation: string }[] = [];
    counties: { Name: string, State: string }[] = [];
    filteredCounties: { Name: string, State: string }[] = [];
    selectedState: string = 'Texas';
    isLoading = true;
    isSaving = false;

    private bidNotificationSettingsService = inject(BidNotificationSettingsService);
    private toastr = inject(ToastrService);
    private fb = inject(FormBuilder);
    private http = inject(HttpClient);

    ngOnInit(): void {
        this.settingsForm = this.fb.group({
            keywords: this.fb.array([]),
            selectedCounties: this.fb.array([])
        });
        this.loadStatesAndCounties();
        this.loadUserSettings();
    }

    onStateChange(stateName: string): void {
        this.selectedState = stateName;
        this.filterCountiesByState(stateName);
    }

    private initializeForm(): void {
        this.settingsForm = this.fb.group({
            keywords: ['', Validators.required],
            selectedCounties: this.fb.array([])
        });
    }

    private loadStatesAndCounties(): void {
        this.http.get<{ Name: string, Abbreviation: string }[]>('assets/data/states.json').subscribe({
            next: states => {
                this.states = states;
                this.selectedState = DEFAULT_STATE; 
                this.filterCountiesByState(this.selectedState); 
            },
            error: () => this.toastr.error('Failed to load states.', 'Error')
        });
    
        this.http.get<{ Name: string, State: string }[]>('assets/data/counties.json').subscribe({
            next: counties => {
                this.counties = counties;
                this.filterCountiesByState(this.selectedState); 
            },
            error: () => this.toastr.error('Failed to load counties.', 'Error')
        });
    }    

    private loadUserSettings(): void {
        this.isLoading = true; 
        this.bidNotificationSettingsService.getSettings().subscribe({
            next: settings => {
                this.populateForm(settings);
                this.isLoading = false; 
            },
            error: () => {
                this.toastr.error('Failed to load settings.', 'Error');
                this.isLoading = false; 
            }
        });
    }

    private populateForm(settings?: BidNotificationSetting): void {
        const selectedCounties = settings?.SelectedCounties?.map(county =>
            this.fb.group({
                State: [county.State, Validators.required],
                County: [county.County, Validators.required]
            })) || [];

        this.settingsForm.setControl('selectedCounties', this.fb.array(selectedCounties));

        const keywords = settings?.Keywords || [];
        this.populateKeywords(keywords);

        this.filterCountiesByState(this.selectedState);
    }

    filterCountiesByState(stateName: string): void {
        const stateAbbreviation = this.states.find(state => state.Name === stateName)?.Abbreviation;
        if (stateAbbreviation) {
            const selectedCounties = this.selectedCountiesFormArray.controls.map(control => control.get('County')?.value);
            this.filteredCounties = this.counties.filter(county =>
                county.State === stateAbbreviation && !selectedCounties.includes(county.Name)
            );
        } else {
            this.filteredCounties = [];
        }
    }    

    addSelectedCounties(selectedOptions: HTMLCollectionOf<HTMLOptionElement>): void {
        const counties = Array.from(selectedOptions).map(option => option.value);
        counties.forEach(county => this.addCounty(county));
    }

    addCounty(countyName: string): void {
        if (!this.isCountySelected(countyName)) {
            this.selectedCountiesFormArray.push(this.fb.group({
                State: [this.selectedState, Validators.required],
                County: [countyName, Validators.required]
            }));
        }
        this.filterCountiesByState(this.selectedState);
    }

    removeSelectedCounties(selectedOptions: HTMLCollectionOf<HTMLOptionElement>): void {
        const countiesToRemove = Array.from(selectedOptions).map(option => option.value);
        countiesToRemove.forEach(countyName => this.removeCounty(countyName));
    }

    removeCounty(countyName: string): void {
        const index = this.selectedCountiesFormArray.controls.findIndex(control =>
            control.get('County')?.value === countyName);
        if (index > -1) {
            this.selectedCountiesFormArray.removeAt(index);
        }
        this.filterCountiesByState(this.selectedState);
    }

    private isCountySelected(countyName: string): boolean {
        return this.selectedCountiesFormArray.controls.some(control => control.get('County')?.value === countyName);
    }

    clearForm(): void {
        this.settingsForm.reset();
        this.selectedCountiesFormArray.clear();
        this.keywordsFormArray.clear(); // Clear all tags/keywords
        this.filterCountiesByState(this.selectedState);
    }

    saveSettings(): void {
        if (this.settingsForm.invalid) {
            this.toastr.error('Please correct errors in the form before saving.', 'Validation Error');
            return;
        }

        this.isSaving = true;

        const keywords = this.keywordsFormArray.value; // Get keywords from the FormArray
        const updatedSettings: BidNotificationSetting = {
            Keywords: keywords,
            SelectedCounties: this.selectedCountiesFormArray.value
        };

        this.bidNotificationSettingsService.upsertSettings(updatedSettings).subscribe({
            next: () =>{
                this.toastr.success('Settings saved successfully!', 'Success');
                this.isSaving = false
            },
            error: () => {
                this.toastr.error('Failed to save settings. Please try again.', 'Error')
                this.isSaving = false;
            }
        });
    }

    get selectedCountiesFormArray(): FormArray {
        return this.settingsForm.get('selectedCounties') as FormArray;
    }

    get keywordsFormArray(): FormArray {
        return this.settingsForm.get('keywords') as FormArray;
    }

    onKeywordInput(event: KeyboardEvent, input: HTMLInputElement): void {
        const value = input.value.trim();
        if ((event.key === ',' || event.key === 'Enter') && value) {
            this.addKeyword(value);
            input.value = '';
            event.preventDefault();
        }
    }

    onKeywordBlur(input: HTMLInputElement): void {
        const value = input.value.trim();
        if (value) {
            this.addKeyword(value);
            input.value = '';
        }
    }

    private hasKeyword(keyword: string): boolean {
        return this.keywordsFormArray.value.includes(keyword);
    }

    addKeyword(keyword: string): void {
        if (!this.hasKeyword(keyword)) {
            this.keywordsFormArray.push(this.fb.control(keyword));
        }
    }

    removeKeyword(index: number): void {
        this.keywordsFormArray.removeAt(index);
    }

    private populateKeywords(keywords: string[]): void {
        keywords.forEach(keyword => this.addKeyword(keyword));
    }
}
