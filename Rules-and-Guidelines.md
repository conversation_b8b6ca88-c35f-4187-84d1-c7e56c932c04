# CivCast Development Rules and Guidelines

## Overview
This document establishes the core rules and guidelines for AI-assisted development of the CivCast construction management platform. These rules ensure consistency, quality, and maintainability across all services and components.

## 1. Architecture and Structure Rules

### 1.1 Service Organization Pattern (MANDATORY)
All services MUST follow the 4-layer architecture:

```
Service.[Domain].[Component].[Layer]/
├── Models/           # Data transfer objects and entities
├── Store/           # Data access layer (repositories, MongoDB clients)
├── System/          # Business logic layer (services, orchestrators)
└── Serverless/      # AWS Lambda functions and handlers
```

### 1.2 Naming Conventions (MANDATORY)
- **New Services**: `Service.[Domain].[Component].[Layer]` (e.g., `Service.Notifications.Email.Store`)
- **Legacy Services**: `CM.[Domain].[Component]` (e.g., `CM.Accounts.Api`)
- **MongoDB Collections**: PascalCase (e.g., `Users`, `Projects`, `BidOpportunities`)
- **MongoDB Fields**: PascalCase (e.g., `Name`, `Email`, `ProjectId`)

### 1.3 Key Service Domains
- **Notifications**: Email orchestration, templates, routing
- **Accounts**: User management, settings, authentication
- **Projects**: Project components, user assignments, cost codes
- **BidOps/Advertisements**: Bid opportunities, documents, planholders
- **Reports**: Various reporting services (daily logs, time cards, etc.)
- **Approvals**: Workflow and approval processes
- **Equipment/Employees**: Resource management

## 2. Data Access Rules (CRITICAL)

### 2.1 Repository Pattern (MANDATORY)
- Services NEVER work directly with MongoDB
- ALL database operations MUST go through repositories
- Store layer handles ALL MongoDB operations
- System layer handles business logic and orchestration

### 2.2 MongoDB Access Standards (MANDATORY)
- Always use `BsonDocument` - NEVER use `dynamic`
- All field access MUST use PascalCase
- Use proper null checking with BsonDocument indexer access
- Collections MUST use PascalCase naming

```csharp
// CORRECT: Repository using BsonDocument with PascalCase
var document = new BsonDocument
{
    ["NotificationId"] = notification.Id,
    ["Email"] = notification.Email,
    ["Status"] = notification.Status
};

// CORRECT: Field access
var userName = user["Name"]?.ToString() ?? "Unknown User";
var userEmail = user["Email"]?.ToString() ?? "<EMAIL>";
```

## 3. Technology Stack Requirements

### 3.1 Core Technologies (MANDATORY)
- **.NET 8**: Primary runtime and framework
- **C#**: Main programming language
- **AWS Lambda**: Serverless compute platform
- **MongoDB**: Primary database (via Amtek.v8.Mongo)
- **AWS Cognito**: User authentication and authorization
- **AWS SES**: Email delivery service
- **AWS SNS/SQS**: Message queuing and pub/sub
- **AWS S3**: Object storage
- **AWS CloudFormation**: Infrastructure as code

### 3.2 API Gateway Usage
- Use AWS API Gateway (HTTP API type preferred) ONLY when HTTP endpoints are required
- Prefer event-driven architecture with SNS/SQS for service communication

## 4. Code Quality Requirements (CRITICAL)

### 4.1 Compilation Verification (MANDATORY)
After ANY code changes:
1. ALWAYS verify compilation using `dotnet build`
2. Run build on affected projects
3. Fix ALL compilation issues before task completion
4. Ensure all using statements are properly included
5. Verify all dependencies are correctly referenced

```powershell
# Verify compilation after code changes
dotnet build ./Service.Example.Store/
dotnet build ./Service.Example.System/
dotnet build ./Service.Example.Serverless/

# Build entire solution if multiple projects affected
dotnet build ConstructionManagement.sln
```

### 4.2 Common Compilation Issues to Check
- Missing using statements for Amtek.v8.* libraries
- Incorrect interface implementations
- Missing dependency injection registrations
- Async/await pattern inconsistencies
- Null reference handling

## 5. Serverless Architecture Guidelines (CRITICAL)

### 5.1 Lambda-First Design Principles (MANDATORY)
- **Stateless Functions**: No persistent state between invocations
- **Short Execution Times**: Design for quick processing (< 5 minutes typical)
- **Event-Driven**: Use SNS/SQS for async communication
- **Cold Start Optimization**: Minimize initialization overhead
- **External State**: Use MongoDB/S3 for persistent data

### 5.2 Lambda Networking Configuration (MANDATORY)
All Lambda functions MUST use these default VPC settings:

```yaml
VpcConfig:
  SubnetIds:
    - subnet-02c715a9690d6ce40
  SecurityGroupIds:
    - sg-0093a9f91155c113b
```

### 5.3 Anti-Patterns to Avoid (FORBIDDEN)
- In-memory caching between invocations
- Long-running background processes
- Stateful circuit breakers without external storage
- Connection pooling (use AWS SDK connection management)
- File system persistence (use S3 instead)
- **Provisioned Concurrency**: NEVER use due to cost implications

## 6. Logging Standards (MANDATORY)

### 6.1 Logging Requirements
- ALWAYS use `ILoggerUtils` interface for logging
- NEVER use `Console.WriteLine()`
- ILoggerUtils MUST be injected via constructor when logging is needed

```csharp
// CORRECT: Logging with ILoggerUtils
public class NotificationService
{
    private readonly ILoggerUtils _logger;

    public NotificationService(ILoggerUtils logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task ProcessNotification()
    {
        _logger.LogInfo("Processing started");
        _logger.LogError("Error occurred", exception);
        _logger.LogWarning("Warning message");
    }
}
```

## 7. Deployment Guidelines

### 7.1 Deployment Commands (MANDATORY)
Always use `dotnet lambda deploy-serverless` with user-specified environment:

```powershell
# Deploy with environment selection
dotnet lambda deploy-serverless -sn "service-name-{environment}" -sb {bucket-name} --profile "{profile-name}" -t "./path/serverless.template" --template-parameters "EnvironmentType={environment}" -c "{environment}"

# Example for dev environment
dotnet lambda deploy-serverless -sn "service-name-dev" -sb civcast-app-dev-functions --profile "aws-cli-build" -t "./Service.Example.Serverless/serverless.template" --template-parameters "EnvironmentType=dev" -c "dev"
```

### 7.2 Environment Configuration
- **dev**: Development environment
- **stage**: Staging environment  
- **prod**: Production environment

## 8. Shared Libraries Usage

### 8.1 Required Amtek Libraries
- **Amtek.v8.Helper.SES**: Email service integration
- **Amtek.v8.Helper.S3**: S3 storage operations
- **Amtek.v8.Helper.SQS**: SQS queue operations
- **Amtek.v8.Helper.SNS**: SNS topic operations
- **Amtek.v8.Mongo**: MongoDB data access
- **Amtek.v8.Logger**: Centralized logging (includes LoggerUtils)
- **Amtek.v8.DependencyInjection**: IoC container setup
- **Amtek.v8.Serverless**: Common serverless utilities

## 9. File Patterns and Standards

### 9.1 Standard File Names
- **serverless.template**: CloudFormation template for AWS resources
- **config-[service]-[env].json**: Environment-specific configuration
- **[Service]Module.cs**: Dependency injection configuration
- **[Service]System.cs**: Main system/business logic class
- **Functions.cs**: Lambda function entry points (in Serverless projects)

## 10. Quality Assurance Checklist

Before completing any development task, verify:
- [ ] Code compiles successfully with `dotnet build`
- [ ] Repository pattern is followed (no direct MongoDB access in services)
- [ ] PascalCase naming is used for MongoDB fields and collections
- [ ] ILoggerUtils is used for logging (not Console.WriteLine)
- [ ] Lambda functions are stateless and event-driven
- [ ] Proper VPC configuration is applied to Lambda functions
- [ ] All using statements for Amtek.v8.* libraries are included
- [ ] Dependency injection is properly configured
- [ ] Error handling and null checking are implemented

## 11. Product Context

CivCast is a comprehensive construction management platform providing:
- **Construction Management**: Project components, employees, equipment, reporting
- **Bid Opportunities**: Document management, planholders, e-bidding, project tracking
- **Notification Services**: Email orchestration, template management, multi-channel delivery
- **User Management**: Accounts, permissions, delegation, role-based access
- **Reporting**: Daily logs, cost codes, time cards, project lists, user roles

Target users are construction companies, contractors, and project managers who need to track bid opportunities, manage projects, and coordinate team communications.
