<div class="container p-4 mb-4">
	<!-- header -->
	<h1 class="fs-5 mb-3">Bid <PERSON>s</h1>
	<p class="mb-3">Bid <PERSON><PERSON><PERSON> will email you about new opportunities that match your selected keywords and
		counties.</p>
	<!-- enter key words-->
	<section class="card mb-4">
		<div class="card-body">
			<h2 class="fs-6">Step 1: Enter keywords related to your bidding interests.
			</h2>
			<p>Type a keyword, such as "water," then press the comma or Enter key to add it to your keyword list. If you
				don't enter any keywords, you'll receive email alerts for all projects in the counties you selected.</p>
			<div class="mb-3">
				<input type="text" class="form-control" (keydown)="onKeywordInput($event, $event.target)"
					(blur)="onKeywordBlur($event.target)"
					placeholder="Type a keyword and then press the comma or enter" />
			</div>
			<div>
				<span *ngFor="let keyword of keywordsFormArray.controls; let i = index" class="badge bg-info me-2">
					{{ keyword.value }}
					<button type="button" class="btn-close btn-sm" aria-label="Remove"
						(click)="removeKeyword(i)"></button>
				</span>
			</div>
		</div>
	</section>
	<!-- select counties -->
	<section class="card mb-3">
		<div class="card-body">
			<h2 class="fs-6">Step 2: Select the counties
				you're interested in.</h2>
			<p>First, select a state. Then, choose your counties and click 'Add Counties.' To
				add
				more counties
				from
				another state, repeat these steps. Use the Ctrl key to select multiple counties or the Shift key
				to
				select a range. Remember to click 'Save' at the bottom of the page when you're finished.
				Double-clicking individual counties to add/remove is also supported.</p>
			<div class="col-12 col-md-6 col-lg-4 mb-3">
				<label for="stateSelect" class="form-label">Select a State</label>
				<select class="form-select" id="stateSelect" (change)="onStateChange($event.target.value)">
					<option *ngFor="let state of states" [value]="state.Name" [selected]="state.Name === 'Texas'">
						{{ state.Name }}
					</option>
				</select>
			</div>
			<div class="row">
				<!-- unselected counties -->
				<div class="col-12 col-md-6 mb-3 mb-md-0">
					<div class="mb-2">
						<label for="selectCounties" class="form-label">Select Counties</label>
						<select class="form-select" id="selectCounties" multiple #selectCounties [disabled]="isLoading">
							@for (county of filteredCounties; track county.Name) {
							<option [value]="county.Name" (dblclick)="addCounty(county.Name)">
								{{ county.Name }}
							</option>
							}
						</select>
					</div>
					<div>
						<button type="button" class="btn btn-outline-dark" [disabled]="isLoading || isSaving"
							(click)="addSelectedCounties(selectCounties.selectedOptions)">Add Selected
							Counties</button>
					</div>
				</div>
				<!-- selected counties -->
				<div class="col-12 col-md-6">
					<div class="mb-2">
						<label for="selectedCounties" class="form-label">Counties You Have Selected</label>
						<select class="form-select" id="selectedCounties" [disabled]="isLoading" multiple
							#selectedCounties>
							@for (county of selectedCountiesFormArray.controls; track $index; let i = $index) {
							<option [value]="county.value.County"
								(dblclick)="removeCounty(county.value.County, county.value.State)">
								{{ county.value.County }}, {{ county.value.State }}
							</option>
							}
						</select>
					</div>
					<div>
						<button type="button" class="btn btn-outline-danger" [disabled]="isLoading || isSaving"
							(click)="removeSelectedCounties(selectedCounties.selectedOptions)">Remove Selected
							Counties</button>
					</div>
				</div>
			</div>
		</div>
	</section>
	<!-- page footer -->
	<footer class="d-flex justify-content-end">
		<button type="button" class="btn btn-outline-dark me-2" (click)="clearForm()"
			[disabled]="isLoading || isSaving">Clear</button>
		<button type="button" class="btn btn-primary" (click)="saveSettings()" [disabled]="isLoading || isSaving">
			@if (isSaving) {
			<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
			}
			Save
		</button>
	</footer>
</div>