﻿<div class="container p-4 mb-4">
  <!--  header -->
  <h1 class="fs-5 mb-3">{{pageTitle}}</h1>
  <!-- add employee -->
  <section>
    <!-- form -->
    @if(isLoading()){
    <div class="placeholder-glow">
      <div class="mb-3" *ngFor="let item of [1,2,3,4]">
        <span class="placeholder col-12" style="height:60px;"></span>
      </div>
    </div>
    }@else {
    <div>
      <form [formGroup]="employeeFormGroup" (submit)="saveEmployee()">
        <div class="mb-3">
          <!-- number -->
          <div class="form-floating mb-3">
            <input class="form-control" type="text" value="" name="customId" id="customId" formControlName="CustomId" />
            <label for="floatingInput">Number</label>
          </div>
          <!-- first name -->
          <div class="form-floating mb-3">
            <input class="form-control" type="search" value="" name="firstName" formControlName="FirstName"
              id="firstname" />
            <label for="floatingInput">First Name</label>
          </div>
          <!-- last name -->
          <div class="form-floating mb-3">
            <input class="form-control" type="search" value="" name="lastName" formControlName="LastName"
              id="lastname" />
            <label for="floatingInput">Last Name</label>
          </div>
          <!-- classification -->
          <div class="form-floating mb-3">
            <input class="form-control" type="search" value="" name="classification" formControlName="Classification"
              id="classification" />
            <label for="floatingInput">Classification</label>
          </div>
          <!-- photo -->
          <!--    <div class="mb-3">
            <label for="formFile" class="form-label">Photo</label>
            <input type="file" class="form-control" id="exampleInputFile" aria-describedby="fileHelp" />
          </div> -->
          <!-- error alert -->
          @if(error()){
          <div class="alert alert-danger" role="alert">
            {{ error() }}
          </div>
          }
        </div>
        <!-- save button -->
        <div class="d-flex justify-content-end">
          <button type="submit" class="btn btn-primary" [disabled]="!employeeFormGroup.valid || isSaving()">
            @if(isSaving()){
            <i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
            }
            Save
          </button>
        </div>
      </form>
    </div>
    }
  </section>
</div>