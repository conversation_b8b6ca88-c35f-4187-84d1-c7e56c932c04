import { Component, OnInit, inject, computed, OnDestroy, effect, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router, RouterModule } from '@angular/router';
import { NgbPaginationModule } from '@ng-bootstrap/ng-bootstrap';
import { PlanOrderInvoicesService } from './plan-order-invoices.service';
import { PlanOrder } from '../../bid-opportunities/shared/interfaces/plan-order';
import { PlanOrdersService } from 'src/app/bid-opportunities/bid/plan-orders/plan-orders.service';
import { InvoiceTableSkeletonComponent } from './invoice-table-skeleton/invoice-table-skeleton.component';

@Component({
  selector: 'app-plan-order-invoices',
  imports: [CommonModule, RouterModule, NgbPaginationModule, InvoiceTableSkeletonComponent],
  templateUrl: './plan-order-invoices.component.html',
  styleUrl: './plan-order-invoices.component.css'
})
export class PlanOrderInvoicesComponent implements OnInit, OnD<PERSON>roy {
  
  /////////// Services //////////
  private readonly planOrderInvoicesService = inject(PlanOrderInvoicesService);
  private readonly router = inject(Router);

  /////////// Utility for template //////////
  readonly Math = Math;

  /////////// Computed from Service //////////
  readonly receipts = this.planOrderInvoicesService.receipts;
  readonly isLoading = this.planOrderInvoicesService.isLoading;
  readonly error = this.planOrderInvoicesService.error;
  readonly total = this.planOrderInvoicesService.total;

  /////////// Computed Properties //////////
  readonly hasReceipts = computed(() => this.receipts().length > 0); 


  /////////// Pagination //////////
  readonly pageSize = this.planOrderInvoicesService.limit;
  readonly currentPage = this.planOrderInvoicesService.currentPage;

  constructor() {

  }

  ngOnInit(): void {
    // Load invoices when component initializes
    this.planOrderInvoicesService.loadReceipts();
  }

  ngOnDestroy(): void {
    //this.planOrderInvoicesService.clearReceipts();
  }
  /////////// Navigation Methods //////////
  viewReceipt(receiptId: string): void {
    this.router.navigate(['/account/plan-order-invoices', receiptId]);
  }

  /////////// Pagination Methods //////////
  onPageChange(page: number): void {
    if(page)
      this.currentPage.set(page);
  }

  /////////// Utility Methods //////////
  formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  formatCurrency(amount: number): string {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  }

  trackByReceiptId(index: number, receipt: PlanOrder): string {
    return receipt.Id || '';
  }
}
