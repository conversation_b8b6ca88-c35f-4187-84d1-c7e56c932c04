import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { Observable } from 'rxjs';
import { AttributeInfo, PolicyInfo } from 'src/app/account/user/shared/interfaces/policy-info';
import { Role } from 'src/app/account/user/shared/interfaces/user-role-store';
import { PermissionsComponent } from '../ui/permissions/permissions.component';
import { ListSkeletonComponent } from 'src/app/components/skeletons/list-skeleton/list-skeleton.component';
import { UserAccessService } from '../../user/shared/data-access/user.access.service';

@Component({
    selector: 'app-role-manager',
    templateUrl: './role-manager.component.html',
    styleUrls: ['./role-manager.component.css'],
    imports: [CommonModule, PermissionsComponent, ListSkeletonComponent]
})
export class RoleManagerComponent implements OnInit {
  private userAccessService = inject(UserAccessService);
  aRouter = inject(ActivatedRoute);
  toastrService = inject(ToastrService);
  role: Role | null = null;
  policies: Array<PolicyInfo> = [];
  isPermissionsLoading: boolean = false;
  isSaving: boolean = false;
  oldRole: Role | null = null;
  hasChange: boolean = false;

  constructor() {}  

  ngOnInit() {
    this.initialize();
  }

  initialize(){

      this.isPermissionsLoading = true;
      const {roleId} = this.aRouter.snapshot.params;

      this.userAccessService.GetRole(roleId).subscribe({
        next:(role) => {
          this.role = role;
          this.userAccessService.GetPolicyInfo(this.role.Policies).subscribe({
            next: (policies) => {
              this.policies = policies;
              this.oldRole = Object.assign({}, this.role);
              this.isPermissionsLoading = false;
            },
            error: (err) => {
              console.log(err);
              this.isPermissionsLoading = false;
            }
          });
  
        },
        error: (err) => {
          console.log(err);
          this.isPermissionsLoading = false;
        }
      });
  }

  
  addPolicy(policyInfo: PolicyInfo) {    
    policyInfo.IsLoading = true;
    if (policyInfo.SelectedAttribute && this.role && policyInfo.SelectedAttribute.Policy) {
      this.userAccessService.AddPolicyToRole(this.role.Id, policyInfo.SelectedAttribute.Policy).subscribe({
        next: (result) => {
          policyInfo.Active = true;
          policyInfo.IsLoading = false;
        },
        error: (err) => {
          console.log(err);
          policyInfo.IsLoading = false;
        }
      });

    } else {
      this.toastrService.warning("Please select an access type");
    }
  }


  

  selectedPolicy(att: AttributeInfo){   
    if(att.Policy && att.PolicyGroup && att.PolicyGroupName){
      if(att.PolicyId === "deny"){
        this.removePolicyFromGroup(att.PolicyGroup, att.PolicyGroupName).subscribe();
      }else{
        if(this.role){
          var hasGroupItem = this.role.Policies?.find(x => x.Id === att.Policy?.Id);
  
          if(!hasGroupItem){
            this.removePolicyFromGroup(att.PolicyGroup, att.PolicyGroupName).subscribe({
              next: (result) => {
                if(att.Policy && this.role){
                  if(!this.role.Policies){
                    this.role.Policies = [];
                  }
                  this.role.Policies.push(att.Policy);
                }else{
                  console.log("Policy is null or role is null");
                }
              },
              error: (err) => {
                console.log(err);
              }
            });
            
          }
        }  
      }  
    }

  }

  removePolicyFromGroup(policyGroup: string, policyGroupName: string): Observable<any>{
    return new Observable((observer) => {
      this.userAccessService.GetGlobalPoliciesByGroup(policyGroup,policyGroupName).subscribe({
        next: (groupInfo) => {
          if(this.role){
            for(let g of groupInfo.PolicyItems){
              var policy = this.role.Policies?.find(x => x.Id == g.Policy?.Id);
              if(policy){
                var idx = this.role.Policies?.indexOf(policy);
                if(idx >= 0){
                  this.role.Policies?.splice(idx, 1);
                } 
              }
            }
          }
          observer.next();
          observer.complete();  
        },
        error: (err) => {
          observer.error(err);          
        }
      });
    });


  }

  saveRole(){
    if(this.role){
      this.isSaving = true;
      this.userAccessService.SaveRole(this.role).subscribe({
        next: (result) => {
          this.toastrService.success("Role saved successfully");
          this.isSaving = false;
        },
        error: (err) => {
          this.isSaving = false;
        }
      });
    }
  }

  removePolicy(policyInfo: PolicyInfo) {
    if(this.role && policyInfo.SelectedAttribute?.Policy){
      policyInfo.IsLoading = true;

      this.userAccessService.RemovePolicyFromRole(this.role.Id, policyInfo.SelectedAttribute.Policy.Id).subscribe({
        next: (result) => {
          policyInfo.IsLoading = false;
        },
        error: (err) => {
          policyInfo.Active = false;
        }
      }); 
    }
  }
}
