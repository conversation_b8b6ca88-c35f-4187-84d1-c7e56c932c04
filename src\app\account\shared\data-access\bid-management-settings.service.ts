import { Injectable, signal } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { BidManagementSetting } from 'src/app/account/shared/interfaces/bid-management-settings.model';

@Injectable({
  providedIn: 'root',
})
export class BidManagementSettingsService {
  private readonly apiUrl = environment.services_root_endpoints.bid_management_settings;
  
  bidManagementSettings = signal<BidManagementSetting | null>(null);

  constructor(private client: HttpClient) { }

  private handleError(err: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    if (err instanceof HttpErrorResponse) {
      errorMessage = `Error ${err.status}: ${err.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }

  addUpdateSettings(settings: BidManagementSetting): Observable<BidManagementSetting> {
    return this.client.post<BidManagementSetting>(this.apiUrl, settings).pipe(
      tap((updatedSettings) => {
        this.bidManagementSettings.set(updatedSettings);
      }),
      catchError(this.handleError)
    );
  }

  getSettings(): Observable<BidManagementSetting> {
    return this.client.get<BidManagementSetting>(this.apiUrl).pipe(
      tap((settings) => this.bidManagementSettings.set(settings)),
      catchError(this.handleError)
    );
  }
}
