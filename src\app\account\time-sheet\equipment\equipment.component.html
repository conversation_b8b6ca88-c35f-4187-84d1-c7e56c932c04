<!-- page header -->
<header class="bg-light p-3 p-lg-4">
	<div class="container">
		<!-- page title -->
		<h1 class="page-title fs-5 mb-3">Equipment</h1>
		<p>Add equipment so foremen can record equipment hours on the time sheet in the diary.</p>
		<div class="row">
			<div class="col-12 col-lg-6 mb-2 mb-lg-0">
				<div class="input-group me-2">
					<span class="input-group-text" id="basic-addon1">
						<i class="fas fa-search" aria-hidden="true"></i>
					</span>
					<input type="text" class="form-control" placeholder="Filter by number, make, model, or notes."
						aria-describedby="basic-addon1" [formControl]="search"
						(input)="searchInputChange.next($event.target.value)" />
				</div>
			</div>
			<div class="col-12 col-lg-3 mb-2 mb-lg-0">
				<div class="btn-group">
					<button type="button" class="btn btn-outline-dark" (click)="gatherAll(EQUIPMENT_STATUS.Active)"
						[ngClass]="{ active: status() === EQUIPMENT_STATUS.Active }">Active</button>
					<button type="button" class="btn btn-outline-dark" (click)="gatherAll(EQUIPMENT_STATUS.Inactive)"
						[ngClass]="{ active: status() === EQUIPMENT_STATUS.Inactive}">
						Inactive
					</button>
					<button type="button" class="btn btn-outline-dark" (click)="gatherAll(EQUIPMENT_STATUS.All)"
						[ngClass]="{ active: status() === EQUIPMENT_STATUS.All }">All</button>
				</div>
			</div>
			<div class="col-12 col-lg-3 mb-2 mb-lg-0 d-lg-flex justify-content-lg-end">
				@if(permissions()?.AddEquipment === ACCESS_EFFECTS.Allow){
				<div class="dropdown">
					<button class="btn btn-outline-dark dropdown-toggle" type="button" id="dropdownMenuButton1"
						data-bs-toggle="dropdown" aria-expanded="false">
						Add Equipment
					</button>
					<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
						<li><a class="dropdown-item" href="javascript:void(0)" (click)="addEquipmentItem()">Add</a></li>
						<li> <a class="dropdown-item" href="javascript:void(0)" (click)="importEquipment()">Import</a>
						</li>
						<li>
							<hr class="dropdown-divider">
						</li>
						<li><a class="dropdown-item" [href]="templateUrl" target="_self">Download Template</a></li>
					</ul>
				</div>
				}
			</div>
		</div>
	</div>
</header>
<!-- equipment -->
<section class="p-3 p-lg-4">
	<div class="container">
		@if(isLoading()){
		<ul class="list-group">
			<li class="list-group-item bg-light" style="height: 50px">
			</li>
			<li class="list-group-item" *ngFor="let item of [1,2,3,4,5,6,7]">
				<div class="placeholder-glow">
					<div class="row">
						<div class="col-12">
							<span class="placeholder col-12" style="height:20px;"></span>
						</div>
					</div>
					<div class="row">
						<div class="col-4">
							<span class="placeholder col-12"></span>
						</div>
					</div>
				</div>
			</li>
		</ul>
		}@else {
		<ul class="list-group">
			@for (item of (equipmentGrid()?.Equipments || []); track $index) {
			<li class="list-group-item d-flex align-items-center justify-content-between">
				<div>
					<div class="d-flex align-items-center gap-2 mb-1">
						<p class="fw-bold mb-0">{{ item.Number }}</p>
						@if(status() === EQUIPMENT_STATUS.All && !item.IsActive) {
							<span class="badge bg-secondary">Inactive</span>
						}
					</div>
					<p class="text-secondary small mb-1">{{ item.Make }} - {{ item.Model }}</p>
					<p class="text-secondary small">
						<span>{{ item.Notes }}</span>
					</p>
				</div>
				@if(permissions()?.AddEquipment === ACCESS_EFFECTS.Allow){
				@if(item.isLoading){
				<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
				}@else {
				<div>
					<div class="dropdown">
						<button class="btn btn-outline-dark dropdown-toggle" type="button" id="dropdownMenuButton1"
							data-bs-toggle="dropdown" aria-expanded="false">
							<i class="fa fa-pencil" aria-hidden="true"></i>
						</button>
						<ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton1">
							<li>
								<a class="dropdown-item" href="javascript:void(0)" (click)="editItem(item)">Edit</a>
							</li>
							<li>
								<a class="dropdown-item" href="javascript:void(0)" (click)="changeStatus(item)">
									@if(item.IsActive){
									Deactivate
									}@else {
									Activate
									}
								</a>
							</li>
						</ul>
					</div>
				</div>
				}
				}
			</li>
			}@empty {
			<li class="list-group-item">
				<div class="alert alert-info m-0" role="alert">
					No equipment.
				</div>
			</li>
			}
		</ul>
		}
	</div>
</section>
<!-- pagination footer following bids.component.html pattern -->
<footer class="p-3 p-lg-4">
	<div class="container">
		@if(equipmentGrid()?.Total && equipmentGrid()?.Total > limit()){
		<div class="d-flex justify-content-end">
			<ngb-pagination
				[collectionSize]="equipmentGrid()?.Total"
				[pageSize]="limit()"
				[page]="currentPage()"
				[rotate]="true"
				[maxSize]="5"
				[boundaryLinks]="true"
				(pageChange)="changePage($event)"
				[disabled]="isLoading()">
				<ng-template ngbPaginationFirst>First</ng-template>
				<ng-template ngbPaginationPrevious>Previous</ng-template>
				<ng-template ngbPaginationNext>Next</ng-template>
				<ng-template ngbPaginationLast>Last</ng-template>
			</ngb-pagination>
		</div>
		}@else {
<!-- 		<div class="text-muted small">
			Total={{equipmentGrid()?.Total}}, Equipment count={{equipmentGrid()?.Equipments?.length}}
		</div> -->
		}
	</div>
</footer>