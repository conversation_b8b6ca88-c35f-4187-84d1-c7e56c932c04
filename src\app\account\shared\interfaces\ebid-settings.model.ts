export interface EBidSettings {
    EBidSettingsId: string;
    UserId: string; 
    Disclaimer: string;
    EBidSettingsDocuments: EBidSettingsDocument[];
}

export interface EBidSettingsDocument {
    DocumentName: string;
    DocumentNotes: string; 
}

export class EBidSettingsRequest {
    constructor(disclaimer: string, documents: EBidSettingsDocument[]) {
        this.Disclaimer = disclaimer;
        this.EBidSettingsDocuments = documents;
    }

    Disclaimer: string;
    EBidSettingsDocuments: EBidSettingsDocument[];
}
