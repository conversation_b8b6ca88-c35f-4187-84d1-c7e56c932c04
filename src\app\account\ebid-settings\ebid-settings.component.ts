import { Component, HostListener, inject, OnDestroy, OnInit } from '@angular/core';
import { EBidSettingsService } from '../shared/data-access/ebid-settings.service';
import { EBidSettingsDocument } from '../shared/interfaces/ebid-settings.model';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { QuillModule } from 'ngx-quill';
import { Subscription } from 'rxjs';
import { NavigationEnd, NavigationStart, Router, RouterEvent } from '@angular/router';

@Component({
    selector: 'app-ebid-settings',
    templateUrl: './ebid-settings.component.html',
    styleUrls: ['./ebid-settings.component.css'],
    imports: [CommonModule, FormsModule, QuillModule],
})
export class EbidSettingsComponent implements OnInit, OnDestroy {
 
    @HostListener('window:beforeunload', ['$event'])
    unloadEvent(event: any) {
      if (this.isDirty()) {
        event.preventDefault();
      }
    }
  
    private ebidSettingsService = inject(EBidSettingsService);        
    private router = inject(Router);
    ebidSettings = this.ebidSettingsService.eBidSettings;
    disclaimer = this.ebidSettingsService.disclaimer;
    newDocumentName: string = '';
    newDocumentNotes: string = '';
    documents = this.ebidSettingsService.documents;    
    isLoading = this.ebidSettingsService.isLoading; // Track loading state
    isSaving = this.ebidSettingsService.isSaving; // Track saving state    
    isDirty = this.ebidSettingsService.isDirty; // Track dirty state
    navigateSubscription: Subscription | null = null;
    quillModules = {
        toolbar: [
            ['bold', 'italic', 'underline'],
            [{ list: 'ordered' }, { list: 'bullet' }],
            ['clean']
        ]
    };

    constructor() {         
 
        this.navigateSubscription = this.router.events.subscribe((event) => {
            const ev = event as RouterEvent;
            if(ev.url && ev.url.indexOf('e-bid/setup') === -1){
              if (event instanceof NavigationStart) {
              
                if (this.isDirty()) {
                  if (!confirm('You have not saved. Do you want to leave?')) {                    
                    this.router.navigateByUrl(this.router.url, { skipLocationChange: true });
                  }else{
                    this.ebidSettingsService.restoreOriginalSettings();
                    this.ebidSettingsService.destroy();
                  }
                }
         
              }else if(event instanceof NavigationEnd){
                  this.ebidSettingsService.destroy();
                  this.router.navigateByUrl(this.router.url, { skipLocationChange: true });                            
              }
            }
          }); 

    }
    ngOnInit(): void {
        this.ebidSettingsService.loadSettings.set(true);   
        
    }

    ngOnDestroy(): void {
      this.navigateSubscription?.unsubscribe();      
    }

    onDisclaimerChange(event:any): void {             
        this.ebidSettingsService.updateDisclaimer(event.html);      
    }

    addDocument(): void {
        this.ebidSettingsService.addDocument(this.newDocumentName, this.newDocumentNotes);
    }

    removeDocument(doc: EBidSettingsDocument): void {
        this.ebidSettingsService.removeDocument(doc);
    }

    saveSettings(): void { 
        this.ebidSettingsService.saveSettings();
    }
}