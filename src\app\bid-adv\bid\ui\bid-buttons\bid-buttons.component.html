<!--buttons-and-validation-messages-->
<div class="row my-4">
	<div class="col-12">
		@if(project()?.PaymentDate){
		<div class="btn-group">
			<button name="updateProject" type="submit" class="btn btn-primary"
				[disabled]="!projectFormGroup.valid || isSaving() || !projectFormGroup.dirty" (click)="update()">
				@if(isSaving()){
					<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
				}
				
				Update Project
			</button>
			<button name="cancelProject" type="button" class="btn btn-secondary" [disabled]="isSaving()"
				(click)="cancelProject()">Cancel</button>
		</div>
		}@else {
		<div class="d-flex justify-content-end">
			<div class="btn-group" role="group" aria-label="Basic example">
				<button name="cancel" type="button" class="btn btn-outline-danger" (click)="cancelProject()"
					[disabled]="isSaving()">Cancel</button>
				<button name="payLater" type="submit" class="btn btn-outline-dark"
					[disabled]="!projectFormGroup.valid || isSaving()" (click)="saveAndPayLater()">Save Draft</button>
				<button name="addToCart" type="submit" class="btn btn-outline-dark"
					[disabled]="!projectFormGroup.valid || isSaving()" (click)="saveAndAddToCart()">Add To Cart</button>
				<button name="payNow" type="submit" class="btn btn-primary" [disabled]="isSaving()"
					(click)="saveAndPay()">
					@if(isSaving()){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
					}
					
					Proceed to Payment
				</button>
			</div>
		</div>
		}
	</div>
</div>