import { Injectable, computed, inject, signal } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { of } from 'rxjs';
import { BidDateTime } from '../interfaces/advertisement';
import { rxResource } from '@angular/core/rxjs-interop';
import { AdvertisementGrid } from '../interfaces/advertisement-grid';

@Injectable()
export class BidsAdvService {
  client = inject(HttpClient);

  advertisementGrid = computed(() => this.advertisementsResource.value());
  isAdvertisementsLoading = computed(() => this.advertisementsResource.isLoading());
  bidDateUpdated = signal<BidDateTime | null>(null);
  search = signal<string | null>(null);
  sortBy = signal<string | null>("BidDetails.BidDateTimeInfo.Date");
  sortOrder = signal<string | null>("desc");
  bidDateStart = signal<string | null>(null);
  bidDateEnd = signal<string | null>(null);
  currentPage = signal<number>(1);
  limit = signal<number>(50);
  initialized = signal<boolean>(false);

  advertisementsResource = rxResource({
    request: () => ({
      search: this.search(),
      sortBy: this.sortBy(),
      sortOrder: this.sortOrder(),
      bidDateStart: this.bidDateStart(),
      bidDateEnd: this.bidDateEnd(),
      page: this.currentPage(),
      limit: this.limit()
    }),
    loader: (request) => {
      if (this.initialized()) {
        let queryParams = new HttpParams(); // Initialize HttpParams

        if (request.request.search) {
          queryParams = queryParams.set('search', request.request.search || '');
        }
        if (request.request.page) {
          queryParams = queryParams.set('page', request.request.page.toString());
        }
        if (request.request.sortBy) {
          queryParams = queryParams.set('sortBy', request.request.sortBy);
        }
        if (request.request.sortOrder) {
          queryParams = queryParams.set('sortOrder', request.request.sortOrder);
        }
        if (request.request.limit) {
          queryParams = queryParams.set('limit', request.request.limit.toString());
        }

        if(request.request.bidDateStart) {
          queryParams = queryParams.set('bidDateStart', request.request.bidDateStart);
        }

        if(request.request.bidDateEnd) {
          queryParams = queryParams.set('bidDateEnd', request.request.bidDateEnd);
        }

        return this.client.get<AdvertisementGrid>(`${environment.services_root_endpoints.advertisements}/`, { params: queryParams });
      }
      return of(null);
    }

  });


  destroy() {
  }
}