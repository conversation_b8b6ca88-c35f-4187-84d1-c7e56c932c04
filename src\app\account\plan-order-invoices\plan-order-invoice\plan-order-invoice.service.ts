import { Injectable, computed, inject, signal } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { rxResource } from '@angular/core/rxjs-interop';
import { of } from 'rxjs';
import { PlanOrder } from '../../../bid-opportunities/shared/interfaces/plan-order';

@Injectable({
  providedIn: 'root'
})
export class PlanOrderInvoiceService {
  private readonly client = inject(HttpClient);
  
  /////////// State Signals //////////
  private readonly _invoiceId = signal<string | null>(null);
  
  /////////// Computed State //////////
  readonly invoiceData = computed(() => this.invoiceResource.value() ?? null);
  readonly isLoading = computed(() => this.invoiceResource.isLoading());
  readonly error = computed(() => this.invoiceResource.error());
  
  /////////// rxResource //////////
  private readonly invoiceResource = rxResource({
    request: () => ({ invoiceId: this._invoiceId() }),
    loader: (request) => {
      if (request.request.invoiceId) {        
        return this.client.get<PlanOrder>(`${environment.services_root_endpoints.bidops_planorders}/invoice/${request.request.invoiceId}`);
      }

      return of(null); // Return null if no invoice ID
    }
  });
  
  /////////// Public Properties //////////
  readonly invoiceId = computed(() => this._invoiceId());
  
  /////////// Public Methods //////////
  
  /**
   * Set the invoice ID to load
   */
  setInvoiceId(invoiceId: string): void {
    this._invoiceId.set(invoiceId);
  }
  
  /**
   * Clear the current invoice
   */
  clearInvoice(): void {
    this._invoiceId.set(null);
  }
}
