import { ComponentFixture, TestBed } from '@angular/core/testing';
import { ActivatedRoute } from '@angular/router';
import { of } from 'rxjs';

import { PlanOrderInvoiceComponent } from './plan-order-invoice.component';

describe('PlanOrderInvoiceComponent', () => {
  let component: PlanOrderInvoiceComponent;
  let fixture: ComponentFixture<PlanOrderInvoiceComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [PlanOrderInvoiceComponent],
      providers: [
        {
          provide: ActivatedRoute,
          useValue: {
            paramMap: of(new Map([['receiptId', '12345']]))
          }
        }
      ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(PlanOrderInvoiceComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should extract receiptId from route parameters', () => {
    expect(component.receiptId()).toBe('12345');
  });
});
