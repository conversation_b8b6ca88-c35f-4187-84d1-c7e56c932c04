<div class="container p-4 mb-4">
    <!-- header -->
    <h1 class="fs-5 mb-3">Addendum Alert Emails</h1>
    <p>Send addendum alerts to additional email addresses.</p>
    <div [formGroup]="settingsForm">
        <!-- contacts section -->
        <section class="card mb-3">
            <div class="card-body">
                <!-- check box - turn on -->
                <div class="form-check mb-3">
                    @if (isLoading) {
                    <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                    } @else {
                    <input type="checkbox" class="form-check-input" id="sendAddendumNotifications"
                        formControlName="sendAddendumNotifications" />
                    <label class="form-check-label" for="sendAddendumNotifications">
                        Turn On
                    </label>
                    }
                </div>
                <!-- add new emails -->
                <div class="row align-items-end mb-4" [formGroup]="newContactForm">
                    <div class="col-12 col-md-4 col-lg-4 mb-3 mb-md-0">
                        <label for="exampleInputEmail1" class="form-label">First and Last Name</label>
                        <input type="text" class="form-control" placeholder="John Smith"
                            formControlName="contactName" />
                    </div>
                    <div class="col-12 col-md-4 col-lg-4 mb-3 mb-md-0">
                        <label for="exampleInputEmail1" class="form-label">Email Address</label>
                        <input type="email" class="form-control" placeholder="<EMAIL>"
                            formControlName="contactEmail" />
                    </div>
                    <div class="col-12 col-md-4 col-lg-4 mb-3 mb-md-0">
                        <button type="button" class="btn btn-outline-dark me-2" (click)="addContact()">Add</button>
                        <button type="button" class="btn btn-outline-dark" (click)="cancelNewContact()">Cancel</button>
                    </div>
                </div>
                <!-- existing contacts -->
                <h2 class="fs-6 mb-2">Emails</h2>
                <table class="table">
                    <thead>
                        <tr class="d-none d-lg-table-row">
                            <th scope="col">Name</th>
                            <th scope="col">Email</th>
                            <th scope="col"></th>
                        </tr>
                    </thead>
                    <tbody>
                        @if (isLoading) {
                        <tr>
                            <td class="placeholder-glow">
                                <span class="placeholder w-100"></span>
                            </td>
                            <td class="placeholder-glow">
                                <span class="placeholder w-100"></span>
                            </td>
                            <td class="placeholder-glow">
                                <span class="placeholder w-100"></span>
                            </td>
                        </tr>
                        } @else {
                        @if(contacts.controls.length === 0){
                        <tr>
                            <td colspan="3">
                                <div class="alert alert-info m-0" role="alert">
                                    No emails added yet.
                                </div>
                            </td>
                        </tr>
                        }
                        @if(contacts){
                        <ng-container formArrayName="contacts">
                            @for(contact of contacts.controls; track contact){
                            <tr [formGroupName]="contacts.controls.indexOf(contact)">
                                <td class="d-lg-none">
                                    @if(editingIndex !== contacts.controls.indexOf(contact)){
                                    <div class="fw-bold mb-1">{{ contact.value.contactName }}</div>
                                    } @else {
                                    <div class="mb-1">
                                        <input type="text" class="form-control" formControlName="contactName" />
                                    </div>
                                    }
                                    @if(editingIndex !== contacts.controls.indexOf(contact)){
                                    <div class="text-muted mb-1">{{ contact.value.contactEmail }}</div>
                                    } @else {
                                    <div class="mb-1">
                                        <input type="email" class="form-control" formControlName="contactEmail" />
                                    </div>
                                    }
                                    @if(editingIndex !== contacts.controls.indexOf(contact)){
                                    <div>
                                        <button type="button" class="btn btn-outline-dark me-2"
                                            (click)="editContact(contacts.controls.indexOf(contact))">
                                            Edit
                                        </button>
                                        <button type="button" class="btn btn-outline-danger"
                                            (click)="removeContact(contacts.controls.indexOf(contact))">
                                            Remove
                                        </button>
                                    </div>
                                    } @else {
                                    <div>
                                        <button type="button" class="btn btn-outline-dark me-2"
                                            (click)="applyContact(contacts.controls.indexOf(contact))">
                                            Apply
                                        </button>
                                        <button type="button" class="btn btn-outline-dark me-2"
                                            (click)="cancelEditContact(contacts.controls.indexOf(contact))">
                                            Cancel
                                        </button>
                                    </div>
                                    }
                                </td>
                                <td class="d-none d-lg-table-cell align-middle">
                                    @if(editingIndex !== contacts.controls.indexOf(contact)){
                                    <span>{{ contact.value.contactName }}</span>
                                    } @else {
                                    <input type="text" class="form-control" formControlName="contactName" />
                                    }
                                </td>
                                <td class="d-none d-lg-table-cell align-middle">
                                    @if(editingIndex !== contacts.controls.indexOf(contact)){
                                    <span class="text-muted">{{ contact.value.contactEmail }}</span>
                                    } @else {
                                    <input type="email" class="form-control" formControlName="contactEmail" />
                                    }
                                </td>
                                <td class="d-none d-lg-table-cell align-middle text-lg-end">
                                    @if(editingIndex !== contacts.controls.indexOf(contact)){
                                    <button type="button" class="btn btn-outline-dark me-2"
                                        (click)="editContact(contacts.controls.indexOf(contact))">
                                        Edit
                                    </button>
                                    } @else {
                                    <button type="button" class="btn btn-outline-dark me-2"
                                        (click)="applyContact(contacts.controls.indexOf(contact))">
                                        Apply
                                    </button>
                                    <button type="button" class="btn btn-outline-dark me-2"
                                        (click)="cancelEditContact(contacts.controls.indexOf(contact))">
                                        Cancel
                                    </button>
                                    }
                                    <button type="button" class="btn btn-outline-danger"
                                        (click)="removeContact(contacts.controls.indexOf(contact))">
                                        Remove
                                    </button>
                                </td>
                            </tr>
                            }
                        </ng-container>
                        }
                        }
                    </tbody>
                </table>
            </div>
        </section>
    </div>
    <!-- page footer -->
    <footer class="d-flex justify-content-end">
        <button type="button" class="btn btn-primary" (click)="saveSettings()" [disabled]="isSaving || isLoading">
            @if (isSaving) {
            <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
            } Save
        </button>
    </footer>
</div>