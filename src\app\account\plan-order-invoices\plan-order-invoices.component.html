<div class="container p-4 mb-4">
  <!-- header -->
  <h1 class="fs-5 mb-3">Plan Order Invoices</h1>



  <!-- Error State -->
  @if (error()) {
    <div class="card p-3 mb-3">
      <div class="alert alert-danger text-center">
        <i class="fas fa-exclamation-triangle fa-3x mb-3"></i>
        <h4 class="alert-heading">Unable to Load Invoices</h4>
        <p class="mb-3">{{ error()?.message || 'An error occurred while loading your invoices.' }}</p>
        <button class="btn btn-primary" (click)="planOrderInvoicesService.refreshReceipts()">
          <i class="fas fa-redo me-2"></i>Try Again
        </button>
      </div>
    </div>
  }

  <!-- Loading State -->
  @if (isLoading()) {
    <app-invoice-table-skeleton [rows]="10"></app-invoice-table-skeleton>
  }@else {
      <!-- Invoices List -->
  @if (!error()) {
    <section class="card p-3 mb-3">
      <table class="table">
        <thead>
          <tr class="d-none d-lg-table-row">
            <th scope="col">
              <span class="fw-bold text-dark text-nowrap">PO Number</span>
            </th>
            <th scope="col">
              <span class="fw-bold text-dark text-nowrap">Date</span>
            </th>
            <th scope="col">
              <span class="fw-bold text-dark text-nowrap">Project</span>
            </th>
            <th scope="col">
              <span class="fw-bold text-dark text-nowrap">Items</span>
            </th>
            <th scope="col">
              <span class="fw-bold text-dark text-nowrap">Total</span>
            </th>
            <th scope="col">
              <span class="fw-bold text-dark text-nowrap">Carrier</span>
            </th>
            <th scope="col">
              <span class="fw-bold text-dark text-nowrap"></span>
            </th>
          </tr>
        </thead>
        <tbody>
          @for(receipt of receipts(); track trackByReceiptId($index, receipt)) {
          <tr>
            <!-- Mobile View (stacked) -->
            <td class="d-lg-none">
              <div class="fw-bold mb-1">{{ receipt.PoNumber }}</div>
              <div class="mb-1">{{ formatDate(receipt.OrderDate) }}</div>
              @if (receipt.ProjectTitle) {
                <div class="mb-1">
                  <div class="fw-bold text-truncate" [title]="receipt.ProjectTitle">
                    {{ receipt.ProjectTitle }}
                  </div>
                </div>
              }
              <div class="mb-1">{{ receipt.Items.length }} item(s) - {{ formatCurrency(receipt.Total) }}</div>
              <div class="mb-1">
                <small>{{ receipt.Carrier }}</small>
              </div>
              <div>
                <button class="btn btn-outline-dark" (click)="viewReceipt(receipt.Id)">
                  <i class="fas fa-eye"></i>
                </button>
              </div>
            </td>
              <!-- Desktop View (table columns) -->
            <td class="d-none d-lg-table-cell align-middle">
              <div class="fw-bold">{{ receipt.PoNumber }}</div>
            </td>
            <td class="d-none d-lg-table-cell align-middle">
              <small class="text-muted">{{ formatDate(receipt.OrderDate) }}</small>
            </td>
            <td class="d-none d-lg-table-cell align-middle">
              @if (receipt.ProjectTitle) {
                <div class="fw-bold text-truncate" style="max-width: 200px;" [title]="receipt.ProjectTitle">
                  {{ receipt.ProjectTitle }}
                </div>
              } @else {
                <span class="text-muted">-</span>
              }
            </td>
            <td class="d-none d-lg-table-cell align-middle">
              <span>{{ receipt.Items.length }}</span>
            </td>
            <td class="d-none d-lg-table-cell align-middle">
              <span class="fw-bold text-primary">{{ formatCurrency(receipt.Total) }}</span>
            </td>
            <td class="d-none d-lg-table-cell align-middle">
              <small>{{ receipt.Carrier }}</small>
            </td>
            <td class="d-none d-lg-table-cell align-middle text-end">
              <button class="btn btn-outline-dark" (click)="viewReceipt(receipt.Id)">
                <i class="fas fa-eye"></i>
              </button>
            </td>
          </tr>
          } @empty {
          <tr>
            <td colspan="7">
              <div class="alert alert-info m-0" role="alert">
                You haven't placed any plan orders yet.
              </div>
            </td>
          </tr>
          }
        </tbody>
      </table>

      <!-- Pagination Controls -->
      @if(total() > pageSize()) {    
        <ngb-pagination [collectionSize]="total()" [pageSize]="pageSize()" [(page)]="currentPage" [rotate]="true" [maxSize]="5"
          [boundaryLinks]="true" (pageChange)="onPageChange($event)">
          <ng-template ngbPaginationFirst>First</ng-template>
          <ng-template ngbPaginationPrevious>Previous</ng-template>
          <ng-template ngbPaginationNext>Next</ng-template>
          <ng-template ngbPaginationLast>Last</ng-template>
        </ngb-pagination>
      }

      </section>
    }
  }

</div>
