{"Information": ["This file provides default values for the deployment wizard inside Visual Studio and the AWS Lambda commands added to the .NET Core CLI.", "To learn more about the Lambda commands with the .NET Core CLI execute the following command at the command line in the project root directory.", "dotnet lambda help", "All the command line options for the Lambda command can be specified in this file."], "profile": "aws-cli-build", "region": "us-east-1", "configuration": "Release", "s3-prefix": "Service.Notifications.Orchestrator.Serverless/", "template": "serverless.template", "template-parameters": "\"environment\"=\"dev\";\"VpcSubnetIds\"=\"subnet-02c715a9690d6ce40\";\"VpcSecurityGroupIds\"=\"sg-0093a9f91155c113b\"", "s3-bucket": "civcast-app-dev-functions", "stack-name": "orchestrator-service-dev"}