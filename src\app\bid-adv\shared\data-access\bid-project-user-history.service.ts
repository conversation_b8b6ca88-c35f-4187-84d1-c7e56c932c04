import { Injectable, inject, signal, computed } from "@angular/core";
import { rxResource } from '@angular/core/rxjs-interop';
import { HttpClient } from '@angular/common/http';
import { of } from 'rxjs';

import { ProjectHistoryGrid } from '../interfaces/project-history-grid';
import { environment } from 'src/environments/environment';

@Injectable()
export class AdvertisementProjectUserHistoryService{

	search = signal<string | null>(null);
	projectId = signal<string | null>(null);
	http = inject(HttpClient);
	sortBy = signal<string>('DateCreated');
	sortOrder = signal<string>('desc');	
	projectUserHistory = computed(() => this.projectUserHistoryResource.value());
	isLoading = computed(() => this.projectUserHistoryResource.isLoading());
	error = computed(() => this.projectUserHistoryResource.error());

	projectUserHistoryResource = rxResource({
		request: () => ({
			search: this.search(),
			projectId: this.projectId(),
			sortBy: this.sortBy(),
			sortOrder: this.sortOrder()
		}),
		loader: (request) => {
			if (request.request.projectId) {
				const params: any = {};
				if (request.request.search) params.search = request.request.search;
				if (request.request.sortBy) params.sortBy = request.request.sortBy;
				if (request.request.sortOrder) params.sortOrder = request.request.sortOrder;
				return this.http.get<ProjectHistoryGrid>(`${environment.services_root_endpoints.adverts_project_user_history}/${request.request.projectId}`, { params });
			}
			return of(null);
		}
	});


}