import { computed, effect, EffectRef, inject, Injectable, Injector, signal } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, of, throwError } from 'rxjs';
import { environment } from 'src/environments/environment';
import { EBidSettings, EBidSettingsDocument, EBidSettingsRequest } from '../interfaces/ebid-settings.model';
import { rxResource } from '@angular/core/rxjs-interop';
import { ToastrService } from 'ngx-toastr';
import { BaseEffectsService } from 'src/app/shared/interfaces/abstract-effects-service';
import { HelperTools } from 'src/app/shared/utils/helper-tools';

@Injectable({
    providedIn: 'root'
})
export class EBidSettingsService extends BaseEffectsService {
    private readonly apiUrl = environment.services_root_endpoints.eBidSettings;
    toastr = inject(ToastrService);
    client = inject(HttpClient);
    loadSettings = signal<boolean | null>(null);
    eBidSettings = computed(() => this.eBidSettingsResource.value());
    isLoading = computed(() => this.eBidSettingsResource.isLoading());
    isSaving = signal<boolean>(false);
    disclaimer = computed(() => this.eBidSettingsResource.value()?.Disclaimer || '');
    isDirty = signal<boolean>(false);
    documents = computed(() => this.eBidSettingsResource.value()?.EBidSettingsDocuments || []);

    private effectsInjector = inject(Injector);


    constructor() {
        super();

    }

    eBidSettingsResource = rxResource({
        request: () => this.loadSettings(),
        loader: () => {
            if (this.loadSettings()) {
                return this.client.get<EBidSettings>(this.apiUrl);
            }

            return of(null);
        }
    });

    destroy() {        
        this.isDirty.set(false);
    }




    restoreOriginalSettings() {
       this.loadSettings.set(false);
    }


    saveSettings(settings: EBidSettingsRequest | null = null) {
        this.isSaving.set(true);

        if (!settings) {
            settings = {
                Disclaimer: this.disclaimer(),
                EBidSettingsDocuments: this.documents()
            } as EBidSettingsRequest;
        }

        this.client.post<void>(this.apiUrl, settings).subscribe({
            next: () => {
                this.eBidSettingsResource.update(() => settings as EBidSettings);
                this.isSaving.set(false);
                console.log('Settings saved successfully');
                this.isDirty.set(false);

            },
            error: (err) => {
                console.error('Error saving settings', err)
                this.isSaving.set(false);
                this.handleError(err);
            }
        });

    }

    addDocument(newDocumentName: string, newDocumentNotes: string): void {
        if (!newDocumentName.trim()) {
            this.toastr.error('Document name cannot be empty.', 'Error');
            return;
        }

        if (!newDocumentNotes.trim()) {
            this.toastr.error('Document notes cannot be empty.', 'Error');
            return;
        }

        // Check for duplicate document names
        if (this.documents()?.some(doc => doc.DocumentName === newDocumentName.trim())) {
            this.toastr.error('Document with the same name already exists.', 'Error');
            return;
        }


        const newDocument: EBidSettingsDocument = {
            DocumentName: newDocumentName.trim(),
            DocumentNotes: newDocumentNotes.trim()
        };

        this.eBidSettingsResource.update(settings => {
            if (!settings) return settings;

            return {
                ...settings,
                EBidSettingsDocuments: [
                    ...(settings.EBidSettingsDocuments || []),
                    newDocument
                ]
            };
        });

        this.isDirty.set(true);

    }

    removeDocument(doc: EBidSettingsDocument): void {

        this.eBidSettingsResource.update(settings => {
            if (!settings) return settings;
            settings.EBidSettingsDocuments = settings.EBidSettingsDocuments.filter((d: EBidSettingsDocument) => d !== doc);
            return {...settings};
        });
        
        this.isDirty.set(true);
    }

    
    /**
     * Updates the disclaimer text if it has changed after stripping HTML tags and trimming whitespace.
     * 
     * This method compares the new disclaimer text with the current disclaimer text after both have been
     * processed to remove HTML tags and trimmed. If the processed texts are different, it updates the 
     * disclaimer in the eBid settings resource and marks the settings as dirty.
     * 
     * @param newDisclaimer - The new disclaimer text to be updated.
     */
    updateDisclaimer(newDisclaimer: string): void {
        var parser = new DOMParser();
        const parsedDisclaimer = HelperTools.stripHtmlTags(newDisclaimer).trim();
        const parsedOldDisclaimer = HelperTools.stripHtmlTags(this.disclaimer()).trim();
       if(parsedDisclaimer !== parsedOldDisclaimer){     

            this.eBidSettingsResource.update(settings => {
                if (!settings) return settings;
                settings.Disclaimer = newDisclaimer;
                return {...settings};
            });

            this.isDirty.set(true);
        }

    }


    private handleError(err: unknown): Observable<never> {
        let errorMessage = 'An unknown error occurred.';
        if (err instanceof HttpErrorResponse) {
            errorMessage = `Error ${err.status}: ${err.message}`;
        }
        console.error(errorMessage);
        return throwError(() => new Error(errorMessage));
    }
}