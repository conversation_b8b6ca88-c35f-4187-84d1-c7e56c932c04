import { HttpClient, httpResource } from "@angular/common/http";
import { computed, effect, inject, Injectable, signal } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { of } from "rxjs";
import { environment } from "src/environments/environment";
import { Submission } from "../interfaces/ebid";
import { BidViews, EBid } from "src/app/ebid/interfaces/ebid";
import { BidAnalysis, BidderBidInfo, CompanyResult } from "../interfaces/bid-results";
import { BidsAdvDocsService } from "./bids-docs.service";
import { PaperBidService } from "./bids-ebid-paper-bid.service";
import { BidAdvService } from "./bid.service";

@Injectable()
export class BidResultsService{
	httpClient = inject(HttpClient);	
	bidAdvService = inject(BidAdvService);
	documentService = inject(BidsAdvDocsService)
	paperBidService = inject(PaperBidService);
	projectId = computed(() => this.bidAdvService.projectId());
	projectTitle = computed(() => this.bidAdvService.advertisement()?.ProjectTitle ?? "");
	projectNumber = computed(() => this.bidAdvService.advertisement()?.InternalId ?? "");
	results = computed(() => this.bidResults.value());	
    currentBidderBidInfo = computed(() => this.results()?.Submissions.find(x => x.BidderInfo.Id == this.bidderBidInfoId())?.BidderInfo);    
	bidResultsReportInfo = computed(() => this.bidReports.value());
	bidderBidInfoId = signal<string | null>(null);
	rejectBidResponse = signal<BidderBidInfo | null>(null);
	bidsRejecting = signal<string[]>([]);
	bidRejected = signal<BidderBidInfo | null>(null);
	isDownloadingReport = signal<boolean>(false);
	isRebuildingReport = signal<boolean>(false);
	isPostingDocuments = signal<boolean>(false);
	bidResults = rxResource({
		request: () => (this.projectId()),
		loader: (request) => {
			if (request.request) {
				return this.httpClient.get<BidResultsResponse>(`${environment.services_root_endpoints.adverts_ebid}/${request.request}/bid-results`);
			}

			return of(null);
		}
	});

	bidReports = rxResource({
		request: () => (this.projectId()),
		loader: (request) => {
			if (request.request) {
				return this.httpClient.get<BidResultsResponse>(`${environment.services_root_endpoints.adverts_ebid_reports}/${request.request}`);
			}

			return of(null);
		}
	});

	currentBidderBidInfoEffect = effect(() => {
		if(this.currentBidderBidInfo()){
			this.paperBidService.bidderBidInfo.set(this.currentBidderBidInfo() ?? null);
		}
	});

	buildBidReport(){		
		this.isRebuildingReport.set(true);
		this.httpClient.get(`${environment.services_root_endpoints.adverts_ebid_reports_lambda}?projectId=${this.projectId()}&method=zip`).subscribe({
			next: (response: any) => {				
				this.isRebuildingReport.set(false);
				this.bidReports.set(response);
				document.location.href = response.PresignedUrl;
			},
			error: (err) => {
				this.isDownloadingReport.set(false);
				console.error(err);
			}
		});
	}

	destroy(){		
		this.bidResults.set(null);		
	}

	
	rejectBid(submissionId: string, projectId: string = ""){					
		this.bidsRejecting.update(x => [...x, submissionId]);
		this.httpClient.patch<BidderBidInfo>(`${environment.services_root_endpoints.adverts_ebid}/${projectId ?? this.projectId()}/reject-bids/${submissionId}`, {}).subscribe({
			next: (response) => {				
				this.bidRejected.set(response);								
				this.bidsRejecting.update(x => x.filter(y => y != submissionId));
			},
			error: (err) => {
				console.error(err);
			}
		});
	}

	unRejectBid(submissionId: string, projectId: string = ""){					
		this.bidsRejecting.update(x => [...x, submissionId]);
		this.httpClient.patch<BidderBidInfo>(`${environment.services_root_endpoints.adverts_ebid}/${projectId ?? this.projectId()}/remove-rejected-bids/${submissionId}`, {}).subscribe({
			next: (response) => {				
				this.bidRejected.set(response);								
				this.bidsRejecting.update(x => x.filter(y => y != submissionId));
			},
			error: (err) => {
				console.error(err);
			}
		});
	}

	downloadReportFile(){
		this.isDownloadingReport.set(true);
		this.httpClient.get(`${environment.services_root_endpoints.adverts_ebid_reports}/${this.projectId()}/file?project-title=${this.projectTitle()}&project-number=${this.projectNumber()}`).subscribe({
			next: (response: any) => {
				this.isDownloadingReport.set(false);
				document.location.href = response.Message;
			},
			error: (err) => {
				this.isDownloadingReport.set(false);
				console.error(err);
			}
		});
	}

	
    postBidDocuments(value: Partial<{ HasBidResults: boolean | null; HasBidTabs: boolean | null; }>) {
		this.isPostingDocuments.set(true);

		const request = {
			PostBidResults: value.HasBidResults,
			PostBidTabs: value.HasBidTabs
		}
        this.httpClient.post(`${environment.services_root_endpoints.adverts_ebid_reports}/${this.projectId()}/files`, request).subscribe({
			next: (response: any) => {
				this.bidResults.update(x => {
					if (x) {
						x.EBid.PostedDocuments = response.PostedDocuments;
					}
					return x;
				});

				this.isPostingDocuments.set(false);

				this.documentService.documentsResource.reload();
			
			},
			error: (err) => {
				console.error(err);
				this.isPostingDocuments.set(false);
			}
		});
    }

}

export interface EBidReportInfoResponse{
	Id: string;
	ProjectId: string;
	PostBidResults: Date;
	PostBidTabulations: Date;
	ReportGeneratedAt: Date;
	UserIdLastGenerated: string;
}

export interface BidResultsResponse{
	ProjectId: string;
	Submissions: Array<Submission>;
	CompanyResults: Array<CompanyResult>;
	BidAnalysis: Array<BidAnalysis>;
	EBid: EBid;
	WorkOrderType: string;
	WorkOrderUserValue: number;
	WorkOrderTotal: number;
}