# Technology Stack

## Core Technologies

- **.NET 8**: Primary runtime and framework
- **C#**: Main programming language
- **AWS Lambda**: Serverless compute platform
- **AWS API Gateway**: RESTful API management and routing (HTTP API type preferred for performance and cost) - only use when HTTP endpoints are required
- **AWS Cognito**: User authentication and authorization
- **MongoDB**: Primary database (via Amtek.v8.Mongo)
- **AWS SES**: Email delivery service
- **AWS SNS/SQS**: Message queuing and pub/sub
- **AWS S3**: Object storage for templates and documents
- **AWS CloudFormation**: Infrastructure as code via serverless templates

## Build System

- **MSBuild**: Primary build system
- **dotnet CLI**: Command-line interface for .NET operations
- **AWS Lambda Tools**: Deployment tooling for serverless functions

## Code Quality Requirements

**CRITICAL: After any task involving code edits, ensure the code compiles successfully**

### Compilation Verification Steps:

1. **Always verify compilation** after making code changes
2. **Run `dotnet build` on affected projects** to check for compilation errors
3. **Fix any compilation issues** before considering the task complete
4. **Ensure all using statements** are properly included
5. **Verify all dependencies** are correctly referenced

```powershell
# Verify compilation after code changes
dotnet build ./Service.Example.Store/
dotnet build ./Service.Example.System/
dotnet build ./Service.Example.Serverless/

# Build entire solution if multiple projects affected
dotnet build ConstructionManagement.sln
```

### Common Compilation Issues to Check:

- Missing using statements for Amtek.v8.\* libraries
- Incorrect interface implementations
- Missing dependency injection registrations
- Async/await pattern inconsistencies
- Null reference handling

## Deployment Approach

**Always use `dotnet lambda deploy-serverless` for serverless deployments** with user-specified environment selection.

### Common Build Commands

````powershell
# Deploy single service - user chooses environment (dev/stage/prod)
dotnet lambda deploy-serverless -sn "service-name-{environment}" -sb {bucket-name} --profile "{profile-name}" -t "./path/serverless.template" --template-parameters "EnvironmentType={environment}" -c "{environment}"

# Example for dev environment
dotnet lambda deploy-serverless -sn "service-name-dev" -sb civcast-app-dev-functions --profile "aws-cli-build" -t "./Service.Example.Serverless/serverless.template" --template-parameters "EnvironmentType=dev" -c "dev"

# Build with optimized parameters
--msbuild-parameters '/p:Configuration=Release /p:PublishReadyToRun=true /p:TieredCompilation=false /p:TieredCompilationQuickJit=false --self-contained false'

## Serverless Architecture Guidelines

**CRITICAL: All services must be designed for AWS Lambda serverless architecture**

### Lambda-First Design Principles:
- **Stateless Functions**: No persistent state between invocations
- **Short Execution Times**: Design for quick processing (< 5 minutes typical)
- **Event-Driven**: Use SNS/SQS for async communication
- **Cold Start Optimization**: Minimize initialization overhead (without using Provisioned Concurrency)
- **External State**: Use DynamoDB/S3 for persistent data

### Resilience Patterns for Lambda:
- **Retry Policies**: Use within single invocation only
- **Dead Letter Queues**: Primary failure handling mechanism
- **Circuit Breakers**: Avoid in-memory state; use external storage if needed
- **Exponential Backoff**: Appropriate for transient failures
- **Service Limits**: Rely on AWS service throttling and limits

### Anti-Patterns to Avoid:
- In-memory caching between invocations
- Long-running background processes
- Stateful circuit breakers without external storage
- Connection pooling (use AWS SDK connection management)
- File system persistence (use S3 instead)
- **Provisioned Concurrency**: Do NOT use Lambda Provisioned Concurrency due to cost implications

### Lambda Networking Configuration

**Default VPC Configuration for Lambda Functions:**

All Lambda functions should be configured with the following default networking settings in serverless templates:

```yaml
VpcConfig:
  SubnetIds:
    - subnet-02c715a9690d6ce40
  SecurityGroupIds:
    - sg-0093a9f91155c113b
```

**Usage in CloudFormation Templates:**
- **Subnet ID**: `subnet-02c715a9690d6ce40` - Default subnet for Lambda functions
- **Security Group ID**: `sg-0093a9f91155c113b` - Default security group for Lambda functions

These settings ensure Lambda functions have proper network access to MongoDB and other AWS services while maintaining security boundaries.

## Logging Standards
**Always use ILoggerUtils interface for logging, never Console.WriteLine()**

**IMPORTANT: ILoggerUtils must be set up in the constructor when needed**

```csharp
// Correct logging approach - inject ILoggerUtils in constructor
public class NotificationService
{
    private readonly ILoggerUtils _logger;

    // ILoggerUtils MUST be injected via constructor when logging is needed
    public NotificationService(ILoggerUtils logger)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }

    public async Task ProcessNotification()
    {
        _logger.LogInfo("Processing started");
        _logger.LogError("Error occurred", exception);
        _logger.LogWarning("Warning message");
    }
}

// Repository example - only add ILoggerUtils if logging is needed
public class NotificationRepository
{
    private readonly MongoClient _mongoClient;
    private readonly ILoggerUtils _logger;

    // Only inject ILoggerUtils if the class needs logging
    public NotificationRepository(MongoClient mongoClient, ILoggerUtils logger)
    {
        _mongoClient = mongoClient ?? throw new ArgumentNullException(nameof(mongoClient));
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
    }
}

// Avoid Console logging
// Console.WriteLine("message"); // DON'T USE
````

## MongoDB Data Access Standards

**CRITICAL: All MongoDB data models and field access must follow strict naming conventions**

### Data Model Naming Requirements:
- **Collection Names**: Must use PascalCase (e.g., `Users`, `Projects`, `BidOpportunities`)
- **Field Names**: Must use PascalCase in MongoDB documents (e.g., `Name`, `Email`, `ProjectId`)
- **Repository Access**: Always use BsonDocument indexer syntax with PascalCase field names

```csharp
// CORRECT: PascalCase field access
var userName = user["Name"]?.ToString();
var userEmail = user["Email"]?.ToString();
var projectId = project["ProjectId"]?.ToString();

// INCORRECT: camelCase or other casing
var userName = user["name"]?.ToString(); // DON'T USE
var userEmail = user["email"]?.ToString(); // DON'T USE
```

### Repository Implementation Standards:
- **Always use BsonDocument**: Never use `dynamic` for MongoDB operations
- **Consistent Field Naming**: All field access must use PascalCase
- **Type Safety**: Always use proper null checking with BsonDocument indexer access

```csharp
// CORRECT: Repository method using BsonDocument with PascalCase
public async Task<Dictionary<string, object>> GetUserDataAsync(string userId)
{
    var user = await _collection.Find(Builders<BsonDocument>.Filter.Eq("_id", ObjectId.Parse(userId)))
        .FirstOrDefaultAsync();
    
    if (user == null) return new Dictionary<string, object>();
    
    return new Dictionary<string, object>
    {
        ["Name"] = user["Name"]?.ToString() ?? "Unknown User",
        ["Email"] = user["Email"]?.ToString() ?? "<EMAIL>",
        ["ProjectId"] = user["ProjectId"]?.ToString() ?? ""
    };
}
```

## Shared Libraries (amtek-shared)

- **Amtek.v8.Helper.SES**: Email service integration
- **Amtek.v8.Helper.S3**: S3 storage operations
- **Amtek.v8.Helper.SQS**: SQS queue operations
- **Amtek.v8.Helper.SNS**: SNS topic operations
- **Amtek.v8.Mongo**: MongoDB data access
- **Amtek.v8.Logger**: Centralized logging (includes LoggerUtils)
- **Amtek.v8.DependencyInjection**: IoC container setup
- **Amtek.v8.Serverless**: Common serverless utilities

## Environment Configuration

- **dev**: Development environment
- **stage**: Staging environment
- **prod**: Production environment

Each environment has specific AWS profiles, S3 buckets, and configuration parameters defined in the build scripts.
