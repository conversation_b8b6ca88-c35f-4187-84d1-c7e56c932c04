import { Injectable, computed, effect, inject, signal } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { catchError, of, tap } from 'rxjs';
import { Advertisement, BidDateTime, TimeInfo } from '../interfaces/advertisement';
import { rxResource } from '@angular/core/rxjs-interop';
import { HelperTools } from 'src/app/shared/utils/helper-tools';

@Injectable()
export class BidAdvService {
  client = inject(HttpClient);
  advertisement = computed(() => this.advertisementResource.value());
  advertisementId = signal<string | null>(null);
  isAdvertisementLoading = computed(() => this.advertisementResource.isLoading());
  bidDateUpdated = signal<BidDateTime | null>(null);
  projectId = signal<string | null>(null);
  bidDateExpired = computed(() => this.isBidDateExpired(this.advertisement() as Advertisement));

  constructor() {

  }
  advertisementResource = rxResource({
	request: () => this.projectId(),
	loader: (request) => {
	  if (request.request) {
		return this.client.get<Advertisement>(`${environment.services_root_endpoints.advertisements}/${request.request}`);
	}

	  return of(null);
	}
  });

  destroy() {
  }



  isBidDateExpired(advertisement: Advertisement): boolean {
	if (!advertisement) {
	  return false;
	}

	const bidDateCVRT = this.getLocalBrowserBidDateInfo(advertisement);

	if (!bidDateCVRT) {
	  throw new Error("Bid Date is not available for this advertisement");
	}

	return bidDateCVRT < new Date();
  }

  isDateExpired(timeInfo: TimeInfo): boolean {
	if (!timeInfo) {
	  return false;
	}

	const bidDateCVRT = this.getLocalBrowserBidDateInfoByTimeInfo(timeInfo);

	if (!bidDateCVRT) {
	  throw new Error("Bid Date is not available for this advertisement");
	}

	return bidDateCVRT < new Date();
  }
  getLocalBrowserBidDateInfo(advertisement: Advertisement): Date | null {
	if (advertisement?.BidDetails?.BidDateTimeInfo) {
	  const year = advertisement?.BidDetails.BidDateTimeInfo?.Year;
	  let month = null;
	  if (advertisement?.BidDateMonthNatural !== undefined && advertisement?.BidDateMonthNatural !== null) {
		month = advertisement?.BidDateMonthNatural;
	  } else {
		month = advertisement?.BidDetails.BidDateTimeInfo?.Month !== null ? advertisement?.BidDetails.BidDateTimeInfo?.Month + 1 : null;
	  }

	  if (!month) {
		return null;
	  }

	  const day = advertisement?.BidDetails.BidDateTimeInfo?.Day;
	  const hour = advertisement?.BidDetails.BidDateTimeInfo?.Hour;
	  const minute = advertisement?.BidDetails.BidDateTimeInfo?.Minute;
	  const timezone = advertisement?.BidDetails.BidDateTimeInfo?.TimeZone?.ZoneId;
	  const bidDateCVRT = HelperTools.convertToTimeZoneDate(year as number, month as number, day as number, hour as number, minute as number, timezone as string);

	  return bidDateCVRT;
	}

	return null;
  }

  getLocalBrowserBidDateInfoByTimeInfo(timeInfo: TimeInfo): Date | null {
	if (timeInfo) {
	  const year = timeInfo?.Year;
	  let month = timeInfo?.Month !== null ? timeInfo?.Month + 1 : null;

	  if (!month) {
		return null;
	  }

	  const day = timeInfo?.Day;
	  const hour = timeInfo?.Hour;
	  const minute = timeInfo?.Minute;
	  const timezone = timeInfo?.TimeZone?.ZoneId;
	  const bidDateCVRT = HelperTools.convertToTimeZoneDate(year as number, month as number, day as number, hour as number, minute as number, timezone as string);

	  return bidDateCVRT;
	}

	return null;
  }
}