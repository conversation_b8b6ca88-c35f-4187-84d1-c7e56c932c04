import { Injectable, signal } from '@angular/core';
import { HttpClient, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError } from 'rxjs';
import { catchError, tap } from 'rxjs/operators';
import { environment } from 'src/environments/environment';
import { AddendumNotification } from 'src/app/account/shared/interfaces/addendum-notifications.model';

@Injectable({
  providedIn: 'root',
})
export class AddendumNotificationsService {
  private readonly apiUrl = environment.services_root_endpoints.addendum_notifications;
  
  addendumNotifications = signal<AddendumNotification | null>(null);

  constructor(private client: HttpClient) { }

  private handleError(err: unknown): Observable<never> {
    let errorMessage = 'An unknown error occurred';
    if (err instanceof HttpErrorResponse) {
      errorMessage = `Error ${err.status}: ${err.message}`;
    }
    console.error(errorMessage);
    return throwError(() => new Error(errorMessage));
  }

	addUpdateSettings(settings: AddendumNotification): Observable<AddendumNotification> {
		return this.client.post<AddendumNotification>(this.apiUrl, settings).pipe(
		tap((updatedSettings) => {
			this.addendumNotifications.set(updatedSettings);
		}),
		catchError(this.handleError)
		);
	}
  

  getSettings(): Observable<AddendumNotification> {
    return this.client.get<AddendumNotification>(this.apiUrl).pipe(
      tap((settings) => this.addendumNotifications.set(settings)),
      catchError(this.handleError)
    );
  }
}
