import { HttpClient } from "@angular/common/http";
import { computed, effect, inject, signal } from "@angular/core";
import { rxResource } from "@angular/core/rxjs-interop";
import { AbstractControl, FormArray, FormControl, FormGroup, Validators } from "@angular/forms";
import { of } from "rxjs";
import { EBid, EBidSection, EBidSectionTypes } from "src/app/ebid/interfaces/ebid";
import { EBidInfoGatherer } from "src/app/ebid/interfaces/ebid-bid-info-gather";
import { environment } from "src/environments/environment";
import { PaperBid } from "../interfaces/paper-bid";
import { EBidService } from "src/app/ebid/data-access/ebid.service";
import { BidderBidInfo } from "src/app/ebid/interfaces/bidder-bid-info";
import { EBidFolderService } from "src/app/ebid/data-access/bid-folder.service";
import { BidAdvService } from "./bid.service";

export class PaperBidService {
	client = inject(HttpClient);
	eBidFolderService = inject(EBidFolderService);
	bidsAdvService = inject(BidAdvService);
	view = signal<string | null>(null);
	projectId = signal<string | null>(null);
	paperBidId = signal<string | null>(null);
	errorResponse = signal<string | null>(null);
	saveResponse = signal<PaperBid | null>(null);
	deleteResponse = signal<string | null>(null);
	eBidService = inject(EBidService);
	paperBid = computed(() => this.paperBidResource.value());
	isPaperBidLoading = computed(() => this.paperBidResource.isLoading());
	bidderBidInfo = signal<BidderBidInfo | null>(null);	
	eBid = computed(() => {

		if(this.eBidFolderService.eBid()){
			var ebid = this.eBidFolderService.eBid();

			if(ebid){
				var sections = [];

				for(let allowedSection of this.allowedSections()){
					var section = ebid.Sections.find((s: any) => s.SectionType === allowedSection);
					if(section){
						sections.push(section);
					}
				}

				ebid.Sections = [];
				ebid.Sections = sections;

				return ebid;
			}else{
				return null;
			}
		}

		return	this.eBidFolderService.eBid();
	});


	allowedSections = signal<string[]>([
		EBidSectionTypes.BID_FORM,
		EBidSectionTypes.COMPLETION_TIME,
		EBidSectionTypes.WORK_ORDER
	]);
	ebidFormGroup = this.eBidFolderService.ebidFormGroup;

	// ebidResource = rxResource({
	// 	request: () => (this.projectId()),
	// 	loader: (projectId) => {
	// 		if (projectId.request) {
	// 			return this.client.get<EBid>(`${environment.services_root_endpoints.adverts_ebid}/${projectId.request}`);
	// 		}

	// 		return of(null);
	// 	}
	// });

	paperBidResource = rxResource({
		request: () => this.paperBidId(),
		loader: (paperBidId) => {
			if (paperBidId.request) {
				return this.client.get<PaperBid>(`${environment.services_root_endpoints.adverts_ebid}/${this.projectId()}/paper-bid/${paperBidId.request}`);
			}
			return of(null);
		}
	});

	companyInfoGroup = new FormGroup({
		CompanyName: new FormControl(''),
		FirstName: new FormControl(''),
		LastName: new FormControl(''),
		Email: new FormControl(''),
		Phone: new FormControl(''),
		Zip: new FormControl(''),
		Address1: new FormControl(''),
		Address2: new FormControl(''),
		City: new FormControl(''),
		State: new FormControl(''),
		CompanyDescription: new FormControl('')
	});


	paperBidEffect = effect(() => {
		if (this.paperBid()) {
			this.companyInfoGroup.get("CompanyName")?.setValue(this.paperBid()?.CompanyName || '');
			this.companyInfoGroup.get("FirstName")?.setValue(this.paperBid()?.FirstName || '');
			this.companyInfoGroup.get("LastName")?.setValue(this.paperBid()?.LastName || '');
			this.companyInfoGroup.get("Email")?.setValue(this.paperBid()?.Email || '');
			this.companyInfoGroup.get("Phone")?.setValue(this.paperBid()?.Phone || '');			
			this.companyInfoGroup.get("Zip")?.setValue(this.paperBid()?.Zip || '');
			this.companyInfoGroup.get("Address1")?.setValue(this.paperBid()?.Address1 || '');
			this.companyInfoGroup.get("Address2")?.setValue(this.paperBid()?.Address2 || '');
			this.companyInfoGroup.get("City")?.setValue(this.paperBid()?.City || '');
			this.companyInfoGroup.get("State")?.setValue(this.paperBid()?.State || '');
			this.companyInfoGroup.get("CompanyDescription")?.setValue(this.paperBid()?.CompanyDescription || '');
		
			this.bidderBidInfo.set(this.paperBid()?.BidderInfoSection || {} as BidderBidInfo);
		}else{
			this.companyInfoGroup.reset();
			this.bidderBidInfo.set({} as BidderBidInfo);
		}
	});


	eBidEffect = effect(() => {
		if(this.eBid()){
			this.eBidFolderService.eBid.set(this.eBid() as EBid);
		}
	});

	bidderInfoEffect = effect(() => {
		if(this.bidderBidInfo()){
			this.eBidFolderService.bidderBidInfoData.set(this.bidderBidInfo() as BidderBidInfo);
		}
	});

	projectIdEffect = effect(() => {
		if(this.projectId()){
			this.eBidFolderService.projectId.set(this.projectId() as string);
		}
	});

	viewEffect = effect(() => {
		console.log(this.view());
		this.eBidFolderService.view.set(this.view());
	  });
	

	clearValuesByPropertyName(propertyNames: Array<string>) {
		const sections = this.ebidFormGroup.get('sections') as FormArray;
	
		const clearValues = (control: AbstractControl, propertyNames: Array<string>) => {
			if (control instanceof FormGroup) {
				propertyNames.forEach(propertyName => {
					if (control.contains(propertyName)) {
						control.get(propertyName)?.setValue(null); // or set to '' if you prefer
					}
				});
				Object.values(control.controls).forEach(childControl => clearValues(childControl, propertyNames));
			} else if (control instanceof FormArray) {
				control.controls.forEach(childControl => clearValues(childControl, propertyNames));
			}
		};
	
		sections.controls.forEach(section => clearValues(section, propertyNames));
	}
	getEBidSectionFormGroup(section: EBidSection): FormGroup {
		var fg = new FormGroup({
		  section: new FormControl<EBidSection>(section, Validators.required)
		});
	
		return fg;
	  }

	destroy() {
		this.companyInfoGroup.reset();
		this.paperBidResource.set(null);
		this.projectId.set(null);
		// this.ebidResource.set(null);
		this.ebidFormGroup.get('sections')?.reset();
		
	}

	clearForm(){
		this.companyInfoGroup.reset();
		this.clearValuesByPropertyName(['UserValue', 'BidderPrice', 'UserTime','UserCompletionTime']);
	}
	addPaperBid(projectId:string, paperBid: PaperBid) {
		this.client.post<PaperBid>(`${environment.services_root_endpoints.adverts_ebid}/${projectId}/paper-bid`, paperBid).subscribe({
			next: (result) => {				
				this.saveResponse.set(result);
				this.bidsAdvService.advertisementResource.update((advertisement) => {
					if(advertisement){
						advertisement.PaperBidsCount = advertisement.PaperBidsCount + 1;
						return {...advertisement};
					}
					return advertisement;
				});
			},
			error: (err) => {

			}
		});
	}

	replacePaperBid(projectId:string, paperBidId:string, paperBid: PaperBid) {
		this.client.put<PaperBid>(`${environment.services_root_endpoints.adverts_ebid}/${projectId}/paper-bid/${paperBidId}`, paperBid).subscribe({
			next: (result) => {
				if(result){
					this.saveResponse.set(result);
				}else{
					console.error('Paper Bid was returned null, Error saving paper bid');
				}
				
			},
			error: (err) => {
				console.error(err);
			}
		});
	}

	save(saveType: PaperBidSaveType) {
		this.saveResponse.set(null);
		this.errorResponse.set(null);

		this.eBidService.gatherBidInfo(this.ebidFormGroup, new EBidInfoGatherer()).subscribe({
			next: (bidInfo) => {
				var paperBid = {
					...this.companyInfoGroup.value,
					BidderInfoSection: bidInfo
				} as PaperBid;

				if(saveType === PaperBidSaveType.Create){
					this.addPaperBid(this.projectId() as string, paperBid);
				}else if(saveType === PaperBidSaveType.Save){
					if(this.paperBid()){
						paperBid.Id = this.paperBid()?.Id as string;
						paperBid.ProjectId = this.projectId() as string;					
						paperBid.CreatedAt = this.paperBid()?.CreatedAt as Date;
						paperBid.BidderInfoSection.CreatedAt = this.paperBid()?.BidderInfoSection.CreatedAt as Date;
						paperBid.BidderInfoSection.SubmittedAt = this.paperBid()?.BidderInfoSection.SubmittedAt as Date;

						this.replacePaperBid(this.projectId() as string, paperBid.Id as string, paperBid);
					}else{
						this.errorResponse.set('Error saving bid');
					}
					
				}else{
					console.log('Invalid save type');					
				}
			},
			error: (err) => {
				console.log(err);
				this.errorResponse.set('Error saving bid');
			}
		});
	}

	deletePaperBid(paperBidId:string){
		this.deleteResponse.set(null);
		this.errorResponse.set(null);

		this.client.delete(`${environment.services_root_endpoints.adverts_ebid}/${this.projectId()}/paper-bid/${paperBidId}`).subscribe({
			next: (result) => {				
				this.deleteResponse.set(paperBidId);
				this.bidsAdvService.advertisementResource.update((advertisement) => {
					if(advertisement){
						advertisement.PaperBidsCount = advertisement.PaperBidsCount - 1;
						return {...advertisement};
					}
					return advertisement;
				});
			},
			error: (err) => {
				console.error(err);
			}
		});
	}
}

export enum PaperBidSaveType {
	None,
	Save,
	Create
}