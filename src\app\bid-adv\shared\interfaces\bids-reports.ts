export class ReportBuilderRequest{
	constructor(projectId: string){
		this.ProjectId = projectId;
	}
	ProjectId: string;             
	General: <PERSON><PERSON><PERSON> = false;
	Details: <PERSON><PERSON>an = false;
	Files: <PERSON><PERSON><PERSON> = false;
	QA: <PERSON>olean = false;
	Planholders: <PERSON><PERSON><PERSON> = false;
	DownloadHistory: <PERSON><PERSON><PERSON> = false;
	EmailHistory: <PERSON><PERSON>an = false;
	SentMessages: <PERSON>olean = false;
	ActivityHistory: <PERSON><PERSON>an = false;
	Addenda: <PERSON>olean = false;
	UseHeader: <PERSON>olean = false;
	UseFooter: <PERSON><PERSON><PERSON> = false;
	FooterText: string = "";
	HeaderText: string = "";
	PageSize: string = "";
	MarginSize: string = "";
	
  }