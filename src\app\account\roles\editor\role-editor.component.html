<!-- <cm-loading [show]="isLoading"></cm-loading> -->
<div class="container mt-3">
  <div class="row">
    <div class="col-12">
      <h1 class="page-title fs-4">{{ pageTitle}}</h1>
    </div>
    <div class="col-12">
      <form #roleEditor="ngForm" (ngSubmit)="saveRole()">
        <div class="border p-3 mb-3">
          <input type="text" #roleNameInput class="form-control mb-3" name="rolename" id="rolename"
            [(ngModel)]="role.Name" placeholder="Role Name" aria-label="Role Name" aria-describedby="basic-addon2"
            required>
        </div>
        <div class="d-flex justify-content-end">
          <button class="btn btn-primary" type="submit" [disabled]="!roleEditor.form.valid || isLoading">
            @if(isLoading){
              <i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i> 
            }       
            Save
          </button>
        </div>
      </form>
    </div>
  </div>
</div>