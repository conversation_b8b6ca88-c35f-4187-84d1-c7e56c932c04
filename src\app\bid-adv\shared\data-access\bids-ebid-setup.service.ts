import { computed, effect, inject, Injectable, Injector, signal } from "@angular/core";
import { AcknowledgeAddendaSection, BidViews, DocumentInfo, EBid, EBidSection, EBidSectionNames, EBidSectionTypes } from "src/app/ebid/interfaces/ebid";
import { ProjectFile } from "../interfaces/bids-docs";
import { BidsAdvDocsService } from "./bids-docs.service";
import { FormArray, FormGroup } from "@angular/forms";
import { EBidHistoryService } from "src/app/ebid/data-access/ebid-history.service";
import { EBidProjectService } from "./bids-ebid-project.service";
import { ToastrService } from "ngx-toastr";
import { EBidFolderService } from "src/app/ebid/data-access/bid-folder.service";
import { v4 } from "uuid";
import { RequiredDownloadsService } from "src/app/ebid/data-access/required-downloads.service";
import { AddendaAcknowledgeService } from "src/app/ebid/data-access/addenda-acknowledge.service";
import { EBidSettingsService } from "src/app/account/shared/data-access/ebid-settings.service";
import { RequiredUploadService, RequiredUploadSetting } from "src/app/ebid/data-access/required-uploads.service";
import { DisclaimerService } from "src/app/ebid/data-access/disclaimer.service";
import { HttpClient } from "@angular/common/http";
import { environment } from "src/environments/environment";
import { Submission } from "../interfaces/ebid";
import { BaseFormService } from "src/app/ebid/data-access/base-form-service";
import { rxResource } from "@angular/core/rxjs-interop";
import { of } from "rxjs";
import { EBID_SETUP_SECTIONS, EBidSelectedData, WorkingOnBidInfo } from "../interfaces/bid-setup";

@Injectable()
export class BidSetupService extends BaseFormService {
	// eBidService = inject(EBidService);
	eBidHistoryService = inject(EBidHistoryService);
	eBidProjectService = inject(EBidProjectService);
	projectDocsService = inject(BidsAdvDocsService);	
	toastrService = inject(ToastrService);
	eBidFolderService = inject(EBidFolderService);
	requiredDownloadsService = inject(RequiredDownloadsService);
	addendaAckService = inject(AddendaAcknowledgeService);
	ebidSettingsService = inject(EBidSettingsService);
	requiredUploadService = inject(RequiredUploadService);
	disclaimerService = inject(DisclaimerService);
	sections = signal<any>(EBID_SETUP_SECTIONS);	
	selectedSections = signal<Array<EBidSelectedData>>([]);
	isSavingBid = signal<boolean>(false);
	bidSaved = signal<EBid | null>(null);	
	projectId = signal<string | null>(null);
	projectBidDateExpired = signal<boolean>(false);	
	view = signal<BidViews>(BidViews.EDIT);
	latestSubmissions = signal<Array<Submission>>([]);
	isLoading = computed(() => this.eBidProjectService.isLoading() || this.isSavingBid() || this.workingOnBidResource.isLoading());
	isEBidLoading = computed(() => this.eBidProjectService.isLoading());
	documents = this.projectDocsService.currentProjectBidDocs;
	eBid = this.eBidProjectService.eBid;
	httpClient = inject(HttpClient);
	ebidFormGroup = this.eBidFolderService.ebidFormGroup;
	usersWorkingOnBid = computed(() => this.workingOnBidResource.value() || []);
	usersWorkingOnBidLoading = computed(() => this.workingOnBidResource.isLoading());

	workingOnBidResource = rxResource({
		request: () => this.projectId(),
		loader: (request) => {
		  if (request.request) {
			return this.httpClient.get<Array<WorkingOnBidInfo>>(`${environment.services_root_endpoints.adverts_ebid}/${request.request}/working-on`);
		}
	
		  return of(null);
		}
	  });
	constructor() {
		super();

		effect(() => {
			this.eBidFolderService.view.set(this.view());
		});

		effect(() => {	
			let nBidSettings = new Array<RequiredUploadSetting>();
			for (let setting of this.ebidSettingsService.eBidSettings()?.EBidSettingsDocuments || []) {
				const nSEttings = new RequiredUploadSetting(setting.DocumentName, setting.DocumentNotes);
				nBidSettings.push(nSEttings);
			}

			this.disclaimerService.bidSettingDisclaimer.set(this.ebidSettingsService.eBidSettings()?.Disclaimer || '');
			this.requiredUploadService.requiredUploadsSettings.set(nBidSettings);
			
		});

		effect(() => {
			if (this.projectId()) {
				this.ebidFormGroup?.patchValue({ ProjectId: this.projectId() as string }, { emitEvent: false });
				this.eBidHistoryService.redoStack.set([]);
				this.eBidHistoryService.undoStack.set([]);			
				this.ebidSettingsService.loadSettings.set(true);								
				this.eBidFolderService.projectId.set(this.projectId() as string);
			}
		});

		effect(() => {
			if (this.eBidHistoryService.undoStack()) {
				this.checkEbidFormGroupSecions();
			}
		});

		effect(() => {
			if (this.eBidHistoryService.redoStack()) {
				this.checkEbidFormGroupSecions();
			}
		});

		effect(() => {
			this.eBidFolderService.isLoading.set(this.isLoading());
		});

		effect(() => {			
			this.eBidFolderService.eBid.set(this.eBid() as EBid);
			const selectedSections = this.eBid()?.Sections.map(s => { return { name: s.SectionName, type: s.SectionType } as EBidSelectedData });
			this.selectedSections.set(selectedSections || []);			
		});

		effect(() => {
			const section = this.eBidFolderService.addSectionSignal();
			if (section && section.name && section.type) {
				this.selectedSections.update((sections) => {
					return [...sections, { name: section.name, type: section.type } as EBidSelectedData];
				});
			} else {
				console.error('Invalid section data', section);
			}
		});

		effect(() => {
			const removeSection = this.eBidFolderService.removeSectionSignal();
			if (removeSection && removeSection.type) {
				this.selectedSections.update((sections) => {
					return sections.filter((s) => s.type !== removeSection.type);
				});
			}
		});

		effect(() => {
			const documents = this.documents();
			if (documents) {
				const infoDocs = this.getInfoDocs(documents as Array<ProjectFile>);
				
				// Only update if the document count or content has actually changed
				const currentDocs = this.requiredDownloadsService.projectInfoDocuments();
				const hasChanged = !currentDocs || 
					currentDocs.length !== infoDocs.length ||
					currentDocs.some((doc, index) => doc.DocumentId !== infoDocs[index]?.DocumentId);
				
				if (hasChanged) {
					this.requiredDownloadsService.projectInfoDocuments.set(infoDocs);
					this.addendaAckService.projectInfoDocuments.set(infoDocs);
				}
			}
		});

		effect(() => {
			if (this.eBidFolderService.sectionSetupComplete() && this.documents()) {
				this.setupAcknowledgeSection(this.getInfoDocs(this.documents() as Array<ProjectFile>));
			}
		});

		effect(() => {
			if (this.bidSaved()) {
				this.ebidFormGroup.markAsUntouched();
				this.ebidFormGroup.markAsPristine();
				this.ebidFormGroup.updateValueAndValidity();
			}
		});
	}

	sectionsSelected(sectionsInfo: any) {
		if (sectionsInfo.itemValue && sectionsInfo.value) {
			const section = sectionsInfo.value.find((x: any) => x.type === sectionsInfo.itemValue.type);
			if (section) {
				const ebidSection = {
					SectionName: section.name,
					SectionType: section.type,
					SectionId: v4()
				} as EBidSection;
				this.eBidFolderService.addNewSection(ebidSection);

			} else {
				const sectionsArray = this.ebidFormGroup?.get('sections') as FormArray;

				var sectionData = sectionsArray.controls.find((s: any) => s.value.section.SectionType === sectionsInfo.itemValue.type)?.value;

				if (sectionData)
					this.eBidFolderService.removeSection(sectionData.section.SectionId);
			}
		}

		// console.log(sectionsInfo);
	}

	checkEbidFormGroupSecions() {
		const sections = this.ebidFormGroup?.get('sections')?.value as Array<{ section: EBidSection }>;
		this.selectedSections.set(sections.map(s => { return { name: s.section.SectionName, type: s.section.SectionType } as EBidSelectedData }));		
	}


	getInfoDocs(docs: Array<ProjectFile>): Array<DocumentInfo> {		
		var infoDocs = new Array<DocumentInfo>();

		if(docs){
			for (let doc of docs as Array<ProjectFile>) {
				const docInfo = new DocumentInfo(doc.FileId as string, doc.Title as string, doc.Category?.Name as string, doc.Storage?.FileExtension as string);
				infoDocs.push(docInfo);
			}
		}

		return infoDocs;
	}

	setupAcknowledgeSection(projectDocumentInfos: DocumentInfo[]) {

		var sections = this.ebidFormGroup?.get('sections') as FormArray;

		if (!sections) {
			console.error('[ADD_ACK_SECTION] Sections form array is required');
			return;
		}

		const addendaAckSection = sections.controls?.find(s => s.value.section.SectionType === EBidSectionTypes.ADDENDA_ACKNOWLEDGE) as FormGroup;

		if (!addendaAckSection) {
			const addendaSection = this.eBid()?.Sections.find(s => s.SectionType === EBidSectionTypes.ADDENDA_ACKNOWLEDGE) as AcknowledgeAddendaSection;
			const addendaDoc = projectDocumentInfos.find(d => d.Category?.toLocaleLowerCase() === 'addenda');

			if (!addendaSection && addendaDoc) {

				if (this.ebidFormGroup) {
					var nSection = {
						SectionName: EBidSectionNames.ADDENDA_ACKNOWLEDGE,
						SectionType: EBidSectionTypes.ADDENDA_ACKNOWLEDGE,
						SectionId: v4()
					} as EBidSection;


					//this.toastrService.info('An Addenda or Revision document was recently added to the project. Addenda Acknowledge section added automatically');

					sections.push(this.eBidFolderService.getEBidSectionFormGroup(nSection));

					this.ebidFormGroup?.markAsDirty();
					this.ebidFormGroup?.markAsTouched();
					this.ebidFormGroup?.updateValueAndValidity();

				}
			} else {
				const addendaSection = this.eBid()?.Sections.find(s => s.SectionType === EBidSectionTypes.ADDENDA_ACKNOWLEDGE) as AcknowledgeAddendaSection;

				if(addendaSection) {
					const addendaIds = addendaSection.Acknowledgements?.map((a: any) => a.DocumentId);

					const addendaDocs = projectDocumentInfos.filter(d => addendaIds.includes(d.DocumentId));

					if (addendaDocs.length <= 0) {
						sections.removeAt(sections.controls.indexOf(addendaAckSection));
					}
				}
			}

		} else {
			const addendaDoc = projectDocumentInfos.find(d => d.Category?.toLocaleLowerCase() === 'addenda');
			if (!addendaDoc) {
				const idx = sections.controls.indexOf(addendaAckSection);
				if (idx > -1) {
					sections.removeAt(idx);
					//this.toastrService.info('An Addenda or Revision document(s) does not exist on the project anymore. Addenda Acknowledge section removed automatically'); 
					this.ebidFormGroup?.markAsDirty();
					this.ebidFormGroup?.markAsTouched();
					this.ebidFormGroup?.updateValueAndValidity();
				} else {
					console.log(`Addenda section not found at idx: ${idx}`);
				}
			}
		}
	}

	saveBid() {
		if (this.eBid()?.OpenedAt) {
			this.toastrService.error('Bid has already been opened. Cannot save bid');
			return;
		}		


		if(!this.projectId())
		{
			this.toastrService.error('Project Id is required to save bid');
			return;
		}

		this.finalSaveBid();
	}

	finalSaveBid() {
		this.isSavingBid.set(true);
		type BasicEBid = Pick<EBid, 'Id' | 'Sections' | 'ProjectId' | 'Version'>;

		let ebid: BasicEBid = {
			Id: this.eBid()?.Id as string,
			Sections: [],
			IsEnabled: this.ebidFormGroup?.get('IsEnabled')?.value,
			BeginDate: this.ebidFormGroup?.get('BeginDate')?.value,
			BeginTimeZone: this.ebidFormGroup?.get('BeginTimeZone')?.value,
			ProjectId: this.eBid()?.ProjectId,
			Version: this.eBid()?.Version
		} as BasicEBid;

		for (let section of this.ebidFormGroup?.get('sections')?.value as Array<{ section: EBidSection }>) {
			ebid.Sections.push(section.section);
		}

		const projectId = this.projectId() as string;

		if(!projectId) {
			this.toastrService.error('There is a problem saving the bid [PROJID]');
			return;
		}

		ebid.ProjectId = projectId;

		this.eBidProjectService.saveBid(projectId, ebid as EBid).subscribe({
			next: (ebid: EBid) => {
				this.eBidProjectService.ebidResource.update(() => ebid);
				this.isSavingBid.set(false);
				this.ebidFormGroup.markAsUntouched();
				this.ebidFormGroup.markAsPristine();
				this.ebidFormGroup.updateValueAndValidity();
				this.bidSaved.set(ebid);
				this.toastrService.success('Bid saved successfully');
				this.latestSubmissions.set([]);
			},
			error: (err) => {
				this.toastrService.error('Error saving bid');
				this.isSavingBid.set(false);
				console.log(err);
			}
		});
	}
}

