import { ComponentFixture, TestBed } from '@angular/core/testing';

import { InvoiceTableSkeletonComponent } from './invoice-table-skeleton.component';

describe('InvoiceTableSkeletonComponent', () => {
  let component: InvoiceTableSkeletonComponent;
  let fixture: ComponentFixture<InvoiceTableSkeletonComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      imports: [InvoiceTableSkeletonComponent]
    })
    .compileComponents();
    
    fixture = TestBed.createComponent(InvoiceTableSkeletonComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });

  it('should generate correct number of skeleton rows', () => {
    component.rows = 5;
    expect(component.skeletonRows.length).toBe(5);
  });

  it('should use default rows when not specified', () => {
    expect(component.skeletonRows.length).toBe(10);
  });
});
