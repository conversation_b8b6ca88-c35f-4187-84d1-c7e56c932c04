import { Component, On<PERSON><PERSON>roy, OnInit, computed, inject, effect } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ReactiveFormsModule } from '@angular/forms';
import { PlanOrdersService } from './plan-orders.service';
import { ProjectFileInfo } from '../../shared/interfaces/bid-ops-documents';
import { UserService } from '../../../shared/data-access/user.service';
import { CivCastAccountService } from '../../../shared/data-access/civcast-account-service';
import { AccountProfile } from '../../../shared/interfaces/account-profile';
import { PlanOrderItem } from '../../shared/interfaces/plan-order';
import { BidOpsProjectsService } from '../../shared/data-access/bid-ops-projects-service';
import { BidOpsProjectService } from '../../shared/data-access/bid-ops-project.service';
import { PlanOrdersDataService } from './plan-orders-data.service';
import { DocumentsSkeletonComponent } from './documents-skeleton/documents-skeleton.component';

@Component({
  selector: 'app-plan-orders',
  imports: [CommonModule, ReactiveFormsModule, DocumentsSkeletonComponent],
  templateUrl: './plan-orders.component.html',
  styleUrl: './plan-orders.component.css'
})
export class PlanOrdersComponent implements OnInit, OnDestroy {
  /////////// Services //////////
  private readonly planOrdersService = inject(PlanOrdersService);
  private readonly planOrdersDataService = inject(PlanOrdersDataService);
  private readonly bidOpsProjectService = inject(BidOpsProjectService);
  private readonly userService = inject(UserService);
  private readonly accountService = inject(CivCastAccountService);
  /////////// Computed from Service //////////
  isAuthenticated = this.planOrdersService.isAuthenticated;
  isLoading = this.planOrdersService.isLoading;
  isDownloading = this.planOrdersService.isDownloading;
  purchasableDocuments = this.planOrdersService.purchasableDocuments;
  planOrderItems = this.planOrdersService.planOrderItems;
  isSubmittingOrder = this.planOrdersService.isSubmittingOrder;
  orderForm = this.planOrdersService.orderForm;  
  orderTotal = this.planOrdersService.orderTotal;
  orderSubtotal = this.planOrdersService.orderSubtotal;
  taxAmount = this.planOrdersService.taxAmount;
  totalQuantity = this.planOrdersService.totalQuantity;
  itemsFormArray = this.planOrdersService.itemsFormArray;

  /////////// Available Options from Service //////////
  availableSizes = this.planOrdersService.availableSizes;
  availableQuantities = this.planOrdersService.availableQuantities;
  availableCarriers = this.planOrdersService.availableCarriers;
  tax = this.planOrdersService.tax;
  /////////// User Profile Effects //////////
  constructor() {
    // Effect to populate shipping address when account profile is available
    effect(() => {
      const accountProfile = this.accountService.AccountProfile();
      if (accountProfile && this.orderForm()) {
        this.populateShippingAddress(accountProfile);
      }
    });

    effect(() => {
      // Initialize the service with the current project
      this.planOrdersService.projectId.set(this.bidOpsProjectService.projectId());
    });

    effect(() => {
      // Update the order form with the current project
      const project = this.bidOpsProjectService.project();
      if (project) {
        this.planOrdersService.projectTitle.set(project.ProjectTitle || '');
      }
    });

    this.planOrdersDataService.initialized.set(true);
  }
  ngOnInit(): void {
    // The service will handle project initialization through effects
  }
  ngOnDestroy(): void {
    // Cleanup if needed
  }

  /////////// Profile Population //////////
  private populateShippingAddress(accountProfile: AccountProfile): void {
    const orderForm = this.orderForm();
    if (!orderForm || !accountProfile) return;

    // Only populate shipping address if the form is empty to avoid overwriting user input
    const isShippingFormEmpty = !orderForm.get('name')?.value &&
      !orderForm.get('address1')?.value &&
      !orderForm.get('city')?.value;

    if (isShippingFormEmpty) {
      // Construct full name from first and last name
      const fullName = [accountProfile.FirstName, accountProfile.LastName]
        .filter(name => name && name.trim())
        .join(' ');

      orderForm.patchValue({
        name: fullName || '',
        company: accountProfile.Company?.Name || '',
        address1: accountProfile.Company?.Address?.Address1 || '',
        address2: accountProfile.Company?.Address?.Address2 || '',
        city: accountProfile.Company?.Address?.City || '',
        state: accountProfile.Company?.Address?.State?.Abbreviation || '',
        zipCode: accountProfile.Company?.Address?.Zip || ''
      });
    }

    // Auto-populate order details fields if empty
    const fieldsToPopulate: any = {};

    // Populate direct phone number if empty
    if (!orderForm.get('directPhone')?.value) {
      fieldsToPopulate.directPhone = accountProfile.Company?.Phone || '';
    }

    // Populate email receipt if empty
    if (!orderForm.get('emailReceipt')?.value) {
      fieldsToPopulate.emailReceipt = accountProfile.Email || '';
    }

    // Apply all populated fields at once
    if (Object.keys(fieldsToPopulate).length > 0) {
      orderForm.patchValue(fieldsToPopulate);
    }
  }
  /////////// Delegate Methods to Service //////////

  getSizePricePerPage(size: string): number {
    return this.planOrdersService.getSizePricePerPage(size);
  }

  getDocumentPrice(size: string, pageCount: number): number {
    return this.planOrdersService.getDocumentPrice(size, pageCount);
  }

  getSizeLabel(size: string): string {
    return this.planOrdersService.getSizeLabel(size);
  }

  getItemPrice(item: PlanOrderItem): number {
    return this.planOrdersService.getItemPrice(item);
  }

  getItemTotal(item: PlanOrderItem): number {
    return this.planOrdersService.getItemTotal(item);
  }

  removeItem(itemIndex: number): void {
    this.planOrdersService.removeItem(itemIndex);
  }
  downloadDocument(item: PlanOrderItem): void {
    this.planOrdersService.downloadDocument(item);
  }

  submitOrder(): void {
    this.planOrdersService.submitOrder();
  }

  addDocumentToOrder(document: ProjectFileInfo): void {
    this.planOrdersService.addDocumentToOrder(document);
  }

  isDocumentInOrder(document: ProjectFileInfo): boolean {
    return this.planOrdersService.isDocumentInOrder(document);
  }
}
