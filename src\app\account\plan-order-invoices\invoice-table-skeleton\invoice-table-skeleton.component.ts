import { CommonModule } from '@angular/common';
import { Component, Input } from '@angular/core';

@Component({
  selector: 'app-invoice-table-skeleton',
  templateUrl: './invoice-table-skeleton.component.html',
  styleUrls: ['./invoice-table-skeleton.component.css'],
  imports: [CommonModule]
})
export class InvoiceTableSkeletonComponent {
  @Input() rows: number = 10;
  
  get skeletonRows(): number[] {
    return Array.from({ length: this.rows }, (_, i) => i);
  }

  trackByIndex(index: number): number {
    return index;
  }
}
