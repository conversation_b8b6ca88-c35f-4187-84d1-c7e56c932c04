import { EBidSectionNames, EBidSectionTypes } from "src/app/ebid/interfaces/ebid";

export const EBID_SETUP_SECTIONS = [
	  {
		name: EBidSectionNames.DISCLAIMER,
		type: EBidSectionTypes.DISCLAIMER
	  },
	  {
		name: EBidSectionNames.BID_FORM,
		type: EBidSectionTypes.BID_FORM
	  },
	  {
		name: EBidSectionNames.COMPLETION_TIME,
		type: EBidSectionTypes.COMPLETION_TIME
	  },
	  {
		name: EBidSectionNames.REQUIRED_DOWNLOADS,
		type: EBidSectionTypes.REQUIRED_DOWNLOADS
	  },
	  {
		name: EBidSectionNames.REQUIRED_UPLOADS,
		type: EBidSectionTypes.REQUIRED_UPLOADS
	  },
	  {
		name: EBidSectionNames.WORK_ORDER,
		type: EBidSectionTypes.WORK_ORDER
	  }
	]


export class EBidSelectedData {
  name: string = '';
  type: string = '';
}

export interface WorkingOnBidInfo{
	UserInfo: any;
    StartedWorkingAt: Date;
    SubmittedAt: Date | null;
}