import { Injectable, computed, effect, inject, signal } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { of } from 'rxjs';
import { environment } from 'src/environments/environment';
import { GeneralSettings, GeneralSettingsConstants, GeneralSettingsUpdateRequest } from '../interfaces/general-settings-model';
import { rxResource } from '@angular/core/rxjs-interop';
import { FormControl, FormGroup, Validators } from '@angular/forms';
import { ToastrService } from 'ngx-toastr';

@Injectable({
  providedIn: 'root',
})
export class GeneralSettingsService {
  private http = inject(HttpClient);
  toatsrService = inject(ToastrService);
  generalSettings = computed(() => this.generalSettingsResource.value());  
  isLoading = computed(() => this.generalSettingsResource.isLoading());
  isSaving = signal<boolean>(false);
  initialized = signal<boolean>(false);
  errorMessage = signal<string | null>(null);

  generalSettingsForm = new FormGroup({
	HideInfoOnPlanholders: new FormControl<boolean | null>(false, Validators.required),
	VanityId: new FormControl<string | null>(null, Validators.maxLength(GeneralSettingsConstants.MaxVanityIdLength)),
  });

  generalSettingsResource = rxResource({
	request: () => this.initialized(),
	loader: () => {
		if(this.initialized()) {
	  		return this.http.get<GeneralSettings>(`${environment.services_root_endpoints.generalSettings}`);
		}

		return of(null);
	}
  });

  generalSettingsSetup = effect(() => {
	if(this.initialized() && this.generalSettings()) {
		this.generalSettingsForm.patchValue({
			HideInfoOnPlanholders: this.generalSettings()?.HideInfoOnPlanholders ?? false,
			VanityId: this.generalSettings()?.VanityId ?? null
		});			
	}
  });

  //this setting is can be updated on its own
  updateVanityIdOnly(){
	const vanityId = this.generalSettingsForm.get('VanityId')?.value as string | null;

	this.isSaving.set(true);
	var request: GeneralSettingsUpdateRequest = {
		VanityId: vanityId,
		HideInfoOnPlanholders: null
	}
	
	this.update(request as GeneralSettings);
  }

  updateGeneralSettings(){
	const hideInfoOnPlanholders = this.generalSettingsForm.get('HideInfoOnPlanholders')?.value as boolean | null;
	const vanityId = this.generalSettingsForm.get('VanityId')?.value as string | null;

	this.isSaving.set(true);
	var request: GeneralSettingsUpdateRequest = {
		HideInfoOnPlanholders: hideInfoOnPlanholders,
		VanityId: vanityId
	}

	this.update(request as GeneralSettings);
	
  }

  update(request: GeneralSettings){
	this.http.patch<GeneralSettings>(`${environment.services_root_endpoints.generalSettings}`, request).subscribe({
		next: (response: GeneralSettings) => {
			this.generalSettingsResource.set(response);
			this.isSaving.set(false);
			this.generalSettingsForm.markAsPristine();
			this.generalSettingsForm.markAsUntouched();
		},
		error: (error) => {
			if(error.error?.message?.includes('Vanity') && error.error?.message?.includes('is already owned')) {
				const message = `Publisher Identity: ${request.VanityId} is taken. Please choose another one.`;
				this.toatsrService.error(message);
				this.errorMessage.set(message);

			}else{
				console.error(error);
				this.isSaving.set(false);
				this.toatsrService.error('An error occurred while saving the general settings.');
			}				
		}
	});
  }
}