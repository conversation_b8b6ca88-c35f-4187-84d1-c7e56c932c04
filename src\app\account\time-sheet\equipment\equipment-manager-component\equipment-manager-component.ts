﻿import { Component, Input, OnInit, inject, computed, effect } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { ToastrService } from 'ngx-toastr';
import { EquipmentInfo } from 'src/app/construction/shared/interfaces/equipment';
import { EquipmentService } from 'src/app/construction/shared/data-access/equipment.service';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';

@Component({
  selector: 'equipment-manager',
  standalone: true,
  imports: [CommonModule, FormsModule, ReactiveFormsModule],
  templateUrl: './equipment-manager-component.html',
  styleUrls: ['./equipment-manager-component.css']
})
export class EquipmentManagerComponent implements OnInit {
  @Input() equipmentItem: EquipmentInfo = {} as EquipmentInfo;
  @Input() state: string | null = null;
  router = inject(Router);
  aRoute = inject(ActivatedRoute);
  equipmentService = inject(EquipmentService);
  toastr = inject(ToastrService);
  equipmentId: string | null = null;
  pageTitle: string | null = null;

  // Use service's loading state for saving operations and equipment loading
  isSaving = computed(() => this.equipmentService.isSavingEquipment());
  isLoading = computed(() => this.equipmentService.isEquipmentItemLoading());
  equipment = computed(() => this.equipmentService.equipment());
  private shouldNavigateAfterSave = false;
  
  constructor() {
    // Effect to handle navigation after save completion
    effect(() => {
      const isSaving = this.equipmentService.isSavingEquipment();
      if (!isSaving && this.shouldNavigateAfterSave) {
        this.shouldNavigateAfterSave = false;
        this.router.navigate(["./equipment"], { relativeTo: this.aRoute.parent });
      }
    });

    // Effect to handle equipment data updates
    effect(() => {
      const equipment = this.equipment();
      if (equipment) {
        this.equipmentItem = equipment;
      }
    });
  }

  ngOnInit() {
    this.state = this.aRoute.snapshot.data['state'];
    this.pageTitle = this.aRoute.snapshot.data['pageTitle'];

    if (this.state === "edit") {
      this.equipmentId = this.aRoute.snapshot.params['equipmentId'];
      if (this.equipmentId) {
        // Use the new signal-based approach
        this.equipmentService.loadEquipmentItem(this.equipmentId);
      }
    }
  }

  saveItem() {
    if (this.state === "edit") {
      this.shouldNavigateAfterSave = true;
      this.equipmentService.SaveEquipmentItem(this.equipmentItem);
    } else if (this.state === "add") {
      this.equipmentItem.IsActive = true;
      this.shouldNavigateAfterSave = true;
      this.equipmentService.AddEquipment(this.equipmentItem);
    }
  }
}
