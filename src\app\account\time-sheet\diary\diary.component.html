<div class="container p-4 mb-4">
	<!-- header-->
	<h1 class="fs-5 mb-3">Diary Settings</h1>
	<p>Customize your diary by showing or hiding the components. Easily rearrange them with a simple
		drag-and-drop.</p>
	<!-- diary -->
	<section>
		@if(isLoading()){
		}@else {
		<!-- diary sections -->
		<!-- dnd-sortable-container [sortableData]="dailyLogComponent?.Components TODO: fix sortable  dnd-sortable [sortableIndex]="i"  (dragend)="reorderComponents()"-->
		<ul class="list-group" cdkDropList (cdkDropListDropped)="dropDiary($event)">
			@for (component of dailyLogComponent()?.Components; track $index) {
			<li class="list-group-item d-flex align-items-center justify-content-between" cdkDrag>
				<div class="d-flex flex-row">
					<div>
						<i class="fas fa-grip-vertical fa-lg text-secondary me-3"></i>
					</div>
					<div>{{ component.Name }}</div>
				</div>
				<div>
					@if(component.IsActive && component.AllowDeletion){
					<button type="button" class="btn btn-dark" (click)="updateComponent(component)">
						<i class="fal fa-check-square fa-lg"></i>
					</button>
					}@else{
					<button type="button" class="btn btn-outline-dark" (click)="updateComponent(component)">
						<i class="fal fa-square fa-lg"></i>
					</button>
					}
				</div>
			</li>
			}@empty {
			<!--no-components-->
			<div class="col-12">
				<div class="alert alert-info m-0" role="alert">
					No components available.
				</div>
			</div>
			}
		</ul>
		}
	</section>
</div>