import { Injectable, computed, effect, inject, signal } from '@angular/core';
import { HttpClient, HttpHeaders } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { Observable, of, Subscription } from 'rxjs';
import { Dimension, DocumentCategory, GetDocumentInfoResponse, PresignedUrlResponse, ProjectFile } from '../interfaces/bids-docs';
import { BidsAdvService } from './bids.service';
import { rxResource } from '@angular/core/rxjs-interop';
import { BidAdvService } from './bid.service';

@Injectable()
export class BidsAdvDocsService {
	http = inject(HttpClient);
	advertisementService = inject(BidAdvService);
	documentCategories = signal<Array<DocumentCategory> | null>(null);
	pageDimensions = signal<Array<Dimension> | null>(null);
	isProjectDocumentsLoading = signal<boolean>(false);	
	currentProjectBidDocs = computed(() => this.documentsResource.value()); //signal<Array<ProjectFile> | null>(null);
	updatedDocument = signal<ProjectFile | null>(null);
	getDocumentSubscription: Subscription | null = null;
	addDocumentSubscription: Subscription | null = null;
	documentCategoriesSubscription: Subscription | null = null;
	documentDimensionsSubscription: Subscription | null = null;
	projectId = signal<string | null>(null);

	documentsResource = rxResource({
		request: () => this.projectId(),
		loader: (projectId) => {
			if(projectId.request){
				return this.http.get<Array<ProjectFile>>(`${environment.services_root_endpoints.adverts_documents}/${projectId.request}`);
			}

			return of(null);
		}
	});
	
	destroy(){
		this.getDocumentSubscription?.unsubscribe();
		this.addDocumentSubscription?.unsubscribe();
		this.documentCategoriesSubscription?.unsubscribe();
		this.documentDimensionsSubscription?.unsubscribe();				
	}


	getDocumentUploadUrl(projectId:string, key: string, contentType: string): Observable<PresignedUrlResponse> {
		return this.http.get<PresignedUrlResponse>(`${environment.services_root_endpoints.adverts_documents}/${projectId}/services/upload-url?key=${key}&content-type=${contentType}`);
	}

	getDocumentDownloadUrl(projectId:string, key: string, fileName: string): Observable<PresignedUrlResponse> {
		
		return this.http.get<PresignedUrlResponse>(`${environment.services_root_endpoints.adverts_documents}/${projectId}/services/download-url?key=${key}&file-name=${fileName}`);
	}

	getDocumentDownloadUrlRaw(projectId:string, fileId: string, ext: string, title: string): Observable<PresignedUrlResponse> {
		const key = `Projects/${projectId}/${fileId}.${ext}`;
		const fileName = `${title}.${ext}`;

		return this.http.get<PresignedUrlResponse>(`${environment.services_root_endpoints.adverts_documents}/${projectId}/services/download-url?key=${key}&file-name=${fileName}`);
	}
	getDocumentMetaData(projectId:string, key: string): Observable<GetDocumentInfoResponse> {
		return this.http.get<GetDocumentInfoResponse>(`${environment.services_root_endpoints.adverts_documents}/${projectId}/services/metadata?key=${key}`);
	}

	addDocument(projectId: string, addRequest: any) {		
		this.addDocumentSubscription = this.http.post<ProjectFile>(`${environment.services_root_endpoints.adverts_documents}/${projectId}`, addRequest).subscribe({
			next: (response) => {		

				var a = [...this.currentProjectBidDocs() as Array<ProjectFile>];
				a.push(response);

				this.documentsResource.set(a);

				if(addRequest['changeBidDate']){
					this.advertisementService.advertisementResource.update((advertisement) => {
						if(advertisement){
							advertisement.BidDetails.BidDateTimeInfo.Date = addRequest['bidDate'];
							return {...advertisement};
						}
						return advertisement;
					});
				}
				
			},
			error: (err) => {
				console.log(err);
			}
		
		});
	}



	getDocumentCategories() {

		if(!this.documentCategories()){
			const headers = new HttpHeaders({
				'ContentType': 'application/json'
			});
	
			this.documentCategoriesSubscription = this.http.get<Array<DocumentCategory>>('./assets/data/doccategories.json', { headers }).subscribe({
				next: (response) => {
					this.documentCategories.set(response);
				},
				error: (err) => {
					console.log(err);
				}		
			});
		}
	
	}

	getPageDimensions() {

		if(!this.pageDimensions()){
			const headers = new HttpHeaders({
				'ContentType': 'application/json'
			});
	
			this.documentDimensionsSubscription = this.http.get<Array<Dimension>>('./assets/data/page-dimensions.json', { headers }).subscribe({
				next: (response) => {
					this.pageDimensions.set(response);
				},
				error: (err) => {
					console.log(err);
				}
			});
		}
	}

	deleteDocument(projectId: string, fileId: string) {
		this.http.delete<any>(`${environment.services_root_endpoints.adverts_documents}/${projectId}/document/${fileId}`).subscribe({
			next: (response) => {
				var a = this.currentProjectBidDocs()?.filter((doc) => doc.FileId !== fileId) as Array<ProjectFile>;
				this.documentsResource.set(a);			
			},
			error: (err) => {
				console.log(err);
			}
		});
	}

	updateDocument(projectId: string, fileId: string, updateRequest: any) {

		this.http.put<ProjectFile>(`${environment.services_root_endpoints.adverts_documents}/${projectId}/document/${fileId}`, updateRequest).subscribe({
			next: (response) => {
				var a = this.currentProjectBidDocs()?.map((doc) => {
					if(doc.FileId === fileId){
						doc = response;
					}

					return doc;
				}) as Array<ProjectFile>;

				this.documentsResource.set(a);

			},
			error: (err) => {
				console.log(err);
			}
		});
	}
}