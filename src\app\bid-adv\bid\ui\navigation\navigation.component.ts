import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { RouterModule } from '@angular/router';
import { BidsAdvService } from 'src/app/bid-adv/shared/data-access/bids.service';

@Component({
    selector: 'app-bid-project-navigation',
    templateUrl: './navigation.component.html',
    styleUrls: ['./navigation.component.css'],
    imports: [CommonModule, RouterModule]
})
export class BidProjectNavigationComponent implements OnInit {

  constructor() { }

  ngOnInit(): void {
  }

}
