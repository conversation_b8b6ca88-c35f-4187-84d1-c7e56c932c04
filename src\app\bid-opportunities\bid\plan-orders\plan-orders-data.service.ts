import { Injectable, computed, inject, signal } from "@angular/core";
import { HttpClient } from "@angular/common/http";
import { CarrierOption, PlanOrder, SizeOption } from "../../shared/interfaces/plan-order";
import { rxResource } from "@angular/core/rxjs-interop";
import { environment } from "src/environments/environment";

@Injectable({
	  providedIn: 'root'
})
export class PlanOrdersDataService {   

	private readonly http = inject(HttpClient);
    readonly planOrderData = computed(() => this.dataResource.value());
    readonly isLoading = computed(() => this.dataResource.isLoading());
    readonly error = computed(() => this.dataResource.error());
	readonly initialized = signal(false);
	dataResource = rxResource({
	  request: () => this.initialized(),
	  loader: () => {
		return this.http.get<PlanOrderDataConstants>(environment.services_root_endpoints.bidops_planorders + '/data');
	  }
	});
}


export interface PlanOrderDataConstants {
  TaxRate: number;
  AvailableSizes: SizeOption[];
  AvailableCarriers: CarrierOption[];
}