import { Component, OnInit, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DownloadHistoryService } from '../../bid-opportunities/shared/data-access/download-history.service';
import { DownloadHistory, DownloadHistoryInfo, DownloadHistoryRequest, ProjectStatus } from '../../bid-opportunities/shared/interfaces/download-history';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { NgbPagination } from '@ng-bootstrap/ng-bootstrap';
import { FormsModule } from '@angular/forms';
import { Subject, Subscription, debounce, debounceTime, distinct, distinctUntilChanged } from 'rxjs';
import { InnerListSkeletonComponent } from 'src/app/components/skeletons/inner-list-skeleton/inner-list-skeleton.component';
import { SortIconComponent } from 'src/app/shared/ui/sort-icon/sort-icon.component';
import { BidOpsDocumentService } from '../../bid-opportunities/shared/data-access/bid-ops-document.service';

@Component({
    selector: 'app-download-history',
    imports: [CommonModule, RouterLink, NgbPagination, FormsModule, SortIconComponent],
    templateUrl: './download-history.component.html',
    styleUrl: './download-history.component.css'
})
export class DownloadHistoryComponent implements OnInit {

  downloadHistoryService = inject(DownloadHistoryService);
  bidOpsDocumentService = inject(BidOpsDocumentService);
  aRoute = inject(ActivatedRoute);
  route = inject(Router);
  searchUpdate = new Subject<string>();
  debounceTime = 500;
  userDownloadHistory = this.downloadHistoryService.userDownloadHistory;
  isUserDownloadHistoryLoading = this.downloadHistoryService.isUserDownloadHistoryLoading;
  search = '';
  isReversed: boolean = false;
  public readonly PROJECT_STATUS: typeof ProjectStatus = ProjectStatus;
  downloadHistoryRequest: DownloadHistoryRequest = new DownloadHistoryRequest();
  downloadSubscription: Subscription | null = null;
  constructor() {
    this.aRoute.queryParams.subscribe(params => {
      this.downloadHistoryRequest = new DownloadHistoryRequest();
      if (params['Search']) {
        this.search = params['Search'];
      }else{
        this.search = '';
      }   


      this.downloadHistoryRequest = { ...this.downloadHistoryRequest, ...params };

      this.downloadHistoryService.downloadHistoryRequest.set(this.downloadHistoryRequest);
    });

    this.searchUpdate.pipe(debounceTime(this.debounceTime),
      distinctUntilChanged()).subscribe((search) => {
        var fSearch = null;
        if (search) {
          fSearch = search;
        }

        this.route.navigate([],
          {
            queryParams: {
              Search: fSearch
            },
            queryParamsHandling: 'merge',
            relativeTo: this.aRoute
          }
        );
      });
  }

  

  downloadFile(file: DownloadHistory) {
    
    file.IsDownloading = true;    
    
    this.downloadSubscription = this.bidOpsDocumentService.downloadFile(
      file.ProjectId, 
      file.FileId,
      file.ProjectTitle
    ).subscribe({
      next: () => {
        file.IsDownloading = false;        
      },
      error: (error) => {
        console.log(error);
        file.IsDownloading = false;   
      }
    });
  }

  ngOnInit(): void {

    this.route.navigate([],
      {
        queryParams: this.downloadHistoryRequest, queryParamsHandling: 'merge', relativeTo: this.aRoute
      }
    );
  }

  setPage(page: number) {
    this.route.navigate([],
      {
        queryParams: {
          Page: page
        },
        queryParamsHandling: 'merge',
        relativeTo: this.aRoute
      }
    );
  }

  sort(column: string) {

    this.route.navigate([],
      {
        queryParams: {
          SortBy: column,
          SortOrder: this.downloadHistoryRequest.SortOrder === 'desc' ? 'asc' : 'desc'
        },
        queryParamsHandling: 'merge', 
        relativeTo: this.aRoute
      }
    );
  }
}
