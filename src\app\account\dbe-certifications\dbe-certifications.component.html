<div class="container p-4 mb-4">
    <!-- header -->
    <h1 class="fs-5 mb-3">DBE Certifications</h1>
    <p>Select your DBE certifications. Upload your certification documents so general contractors can
        download and verify them. Your certifications will be displayed
        on the
        plan holders list.</p>
    <!-- deb certifications -->
    <section class="mb-3">
        <table class="table">
            @if (isLoading()) {
            <tbody>
                <tr class="placeholder-glow">
                    <td width="130">
                        <span class="placeholder col-12"></span>
                    </td>
                    <td>
                        <span class="placeholder col-12"></span>
                    </td>
                </tr>
                <tr class="placeholder-glow">
                    <td width="130">
                        <span class="placeholder col-12"></span>
                    </td>
                    <td>
                        <span class="placeholder col-12"></span>
                    </td>
                </tr>
                <tr class="placeholder-glow">
                    <td width="130">
                        <span class="placeholder col-12"></span>
                    </td>
                    <td>
                        <span class="placeholder col-12"></span>
                    </td>
                </tr>
                <tr class="placeholder-glow">
                    <td width="130">
                        <span class="placeholder col-12"></span>
                    </td>
                    <td>
                        <span class="placeholder col-12"></span>
                    </td>
                </tr>

            </tbody>
            } @else {
            @if(staticCertifications().length > 0){
            <tbody>
                @for(certification of staticCertifications(); track certification.DBECertId){
                <!-- mobile -->
                <tr class="d-lg-none">
                    <td colspan="4">
                        <div class="form-check mb-0">
                            <input class="form-check-input" type="checkbox"
                                [checked]="isChecked(certification.DBECertId)"
                                (change)="onCertificationChange(certification.DBECertId, $event.target.checked)" />
                            <label class="form-check-label" for="{{ certification.DBECertId }}">
                                <span class="fw-bold me-2">&nbsp;{{ certification.Abbreviation }}</span>
                            </label>
                        </div>
                        <div class="mb-1">
                            {{ certification.Name }}
                        </div>
                        <div>
                            @if(isChecked(certification.DBECertId)){
                            <div>
                                <input type="file" #fileInput class="d-none"
                                    (change)="onFileSelected(certification.DBECertId, $event)" />
                                <button type="button" class="btn btn-outline-dark" (click)="fileInput.click()"
                                    [disabled]="isSaving">
                                    @if (isSaving) {
                                    <span class="spinner-border spinner-border-sm" role="status"
                                        aria-hidden="true"></span>
                                    } Add File
                                </button>
                            </div>
                            }
                        </div>
                        <div>
                            @if(uploadProgress[certification.DBECertId] !== undefined &&
                            uploadProgress[certification.DBECertId] < 100){ <p>Uploading: {{
                                uploadProgress[certification.DBECertId] }}%</p>
                                }
                                @for(doc of getDocuments(certification.DBECertId); track doc.S3Key){
                                <div class="d-flex align-items-center">
                                    <button type="button" class="btn btn-link"
                                        (click)="fetchPresignedUrlForDocument(doc)">{{ doc.FileName }}</button>
                                    <button type="button" class="btn btn-link text-danger" [disabled]="isSaving"
                                        (click)="confirmDeleteDocument(certification.DBECertId, doc)">
                                        Delete
                                    </button>
                                </div>
                                }
                        </div>
                    </td>
                </tr>
                <tr class="d-none d-lg-table-row">
                    <!-- abbreviation and checkbox -->
                    <td width="130">
                        <div class="form-check mb-0">
                            <input class="form-check-input" type="checkbox"
                                [checked]="isChecked(certification.DBECertId)"
                                (change)="onCertificationChange(certification.DBECertId, $event.target.checked)" />
                            <label class="form-check-label" for="{{ certification.DBECertId }}">
                                <span class="fw-bold me-2">&nbsp;{{ certification.Abbreviation }}</span>
                            </label>
                        </div>
                    </td>
                    <!-- full name -->
                    <td>{{ certification.Name }}</td>
                    <!-- add file -->
                    <td>
                        @if(isChecked(certification.DBECertId)){
                        <div>
                            <input type="file" #fileInput class="d-none"
                                (change)="onFileSelected(certification.DBECertId, $event)" />
                            <button type="button" class="btn btn-outline-dark" (click)="fileInput.click()"
                                [disabled]="isSaving">
                                @if (isSaving) {
                                <span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
                                } Add File
                            </button>
                        </div>
                        }
                    </td>
                    <!-- existing certifications -->
                    <td>
                        @if(uploadProgress[certification.DBECertId] !== undefined &&
                        uploadProgress[certification.DBECertId] < 100){ <p>Uploading: {{
                            uploadProgress[certification.DBECertId] }}%</p>
                            }
                            @for(doc of getDocuments(certification.DBECertId); track doc.S3Key){
                            <div class="d-flex align-items-center">
                                <button type="button" class="btn btn-link"
                                    (click)="fetchPresignedUrlForDocument(doc)">{{
                                    doc.FileName }}</button>
                                <button type="button" class="btn btn-link text-danger" [disabled]="isSaving"
                                    (click)="confirmDeleteDocument(certification.DBECertId, doc)">
                                    Delete
                                </button>
                            </div>
                            }
                    </td>
                </tr>
                }
            </tbody>
            }
            }
        </table>
    </section>
    <!-- page-footer -->
    <footer>
        <p class="text-secondary">Missing a certification? Let us know! Email us at <a class="custom-link"
                href="mailto:<EMAIL>">support&#64;civcast.com</a>.</p>
    </footer>
</div>