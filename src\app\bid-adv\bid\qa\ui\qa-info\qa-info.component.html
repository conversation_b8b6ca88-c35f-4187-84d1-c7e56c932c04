<header class="mb-3">
	<h2 class="page-title fs-6 mb-3">Questions</h2>
</header>
<!-- access allowed -->
@if(!isLoading())
{
<!-- tool bar -->
<section class="row align-items-end mb-2">
	<!----  buttons  ------>
	<div class="col-12 col-lg-8 mb-2 order-1 order-lg-2 d-flex justify-content-lg-end">
		<div>
			<button class="btn btn-outline-dark btn-sm me-2" type="button" data-bs-toggle="collapse"
				data-bs-target="#collapseExample" aria-expanded="false" aria-controls="collapseExample">
				Add Your Own Question
			</button>
		</div>
		<div>
			<button class="btn btn-outline-dark btn-sm" (click)="downloadExcel()" [disabled]="isDownloadingExcel()">
				@if(isDownloadingExcel()){
				<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
				}
				Export
			</button>
		</div>
	</div>
	<!-- ask your own question -->
	<div class="collapse order-2 order-lg-3 my-3" id="collapseExample">
		<form [formGroup]="questionForm" (submit)="askQuestion()">
			<section>
				<!-- text-area + submit button -->
				<div class="bg-light rounded p-3">
					<!---------->
					<div class="mb-3">
						<label class="form-label" for="QATextBox">Type your question here.</label>
						<textarea class="form-control" id="QATextBox" name="question" formControlName="question"
							rows="3"></textarea>
					</div>
					<!---------->
					<div class="d-flex justify-content-end">
						<button type="submit" class="btn btn-outline-dark"
							[disabled]="!questionForm.valid || isAskingQuestion()">
							@if(isAskingQuestion()){
								<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
							}
							Add Question
						</button>
					</div>
				</div>
			</section>
		</form>
	</div>
	<!----  filter  ------>
	<div class="col-12 mb-2 col-lg-4 order-3 order-lg-1">
		<label for="exampleInputEmail1" class="form-label">Filter Questions</label>
		<select class="form-select" (change)="filterQuestions($event.target.value)">
			<option [value]="'all'">Show All Questions</option>
			<option [value]="'answered'">Show Only Answered Questions</option>
			<option [value]="'unanswered'">Show Only Unanswered Questions</option>
		</select>
	</div>
</section>
<!--questions + answers -->
<section>
	@for (questionInfo of allQuestions(); track $index) {
	<table class="table table-bordered align-middle mb-4">
		<tbody>
			<!-- question -->
			<tr>
				<td colspan="2" class="bg-light">
					<div class="fw-bold mb-1">{{ $index + 1 }}. <span
							[innerHtml]="questionInfo.question?.Content"></span></div>
					<div class="text-body-secondary small mb-3">Asked on {{ questionInfo.question?.CreatedDate |
						date:
						'M/dd/yyyy h:mm a' }} by 
						@if(questionInfo?.profileLoaded){
						{{questionInfo.profileInfo?.FirstName}}
						{{questionInfo.profileInfo?.LastName}} &#64; {{ questionInfo.profileInfo?.CompanyName}}.
						}@else {
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}
					
					</div>
					<div>
						<app-add-answer [questionId]="questionInfo.question.QuestionId"
							[projectId]="projectId()"></app-add-answer>
					</div>
				</td>
			</tr>
			<!-- answer -->
			@for (answer of questionInfo.question?.Answers; track $index)
			{
			@if(answer){
			<tr>
				@if(answer?.IsDisabled){
				<td>
					<div class="mb-1">
						<span class="badge text-bg-light">{{ answer?.CreateDate | date: 'M/dd/yyyy' }}</span>
					</div>
					<div [innerHTML]="answer?.Content" style="text-decoration:line-through">
					</div>
				</td>
				}@else {
				<td>
					<div class="mb-1">
						<span class="badge text-bg-light">{{ answer?.CreateDate | date: 'M/dd/yyyy' }}</span>
					</div>
					<div [innerHTML]="answer?.Content">
					</div>
				</td>
				}
				<td style="width: 110px;">
					<button class="btn btn-sm btn-outline-danger"
						(click)="disableAnswer(questionInfo.question.QuestionId, answer, answer?.IsDisabled)"
						[disabled]="answer?.IsStrikingOut">
						@if(answer?.IsStrikingOut){
						<i class="fas fa-circle-notch fa-spin fa-1x mx-2"></i>
						}
						@if(answer?.IsDisabled){
						<span>Undo Strike</span>
						}@else {
						<span>Strike</span>
						}
					</button>
				</td>
			</tr>
			}
			}
			@empty
			{
			<tr>
				<td class="text-info">
					No answers yet.
				</td>
			</tr>
			}
		</tbody>
	</table>
	}
</section>
}
@else
{
<app-qa-skeleton></app-qa-skeleton>
}