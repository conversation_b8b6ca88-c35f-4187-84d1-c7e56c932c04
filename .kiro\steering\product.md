# Product Overview

CivCast is a comprehensive construction management platform that provides bid opportunity management, project tracking, and notification services for the construction industry.

## Core Features
- **Construction Management**: Project components, employees, equipment, and reporting
- **Bid Opportunities**: Document management, planholders, e-bidding, and project tracking
- **Notification Services**: Email orchestration, template management, and multi-channel delivery
- **User Management**: Accounts, permissions, delegation, and role-based access
- **Reporting**: Daily logs, cost codes, time cards, project lists, and user roles

## Architecture
The platform follows a microservices architecture deployed on AWS using serverless functions (Lambda), with services organized by domain (Accounts, Projects, BidOps, Notifications, etc.). Each service typically includes API, Store, System, and Serverless components.

## Target Users
Construction companies, contractors, and project managers who need to track bid opportunities, manage projects, and coordinate team communications.