﻿<div class="container p-4 mb-4">
  <!--  header -->
  <h1 class="fs-5 mb-3">Import Employees</h1>
  <p>Import employees from an Excel file. For the fastest import use our Excel template.</p>
  <!-- import employees -->
  <section>
    <div class="placeholder-glow" *ngIf="isLoading">
      <span class="placeholder col-12" style="height: 60px;"></span>
    </div>
    <!--form-->
    <div *ngIf="!isLoading">
      <!--file input-->
      <div class="btn-group">
        @if(!employeeImportResponse?.IsPartial){
        <div>
          <civcast-aws-uploader [maxFiles]="1" (uploadError)="error($event)" (selectedFiles)="setSelectedFiles($event)"
            [allowDragDrop]="false" [allowFolderFiles]="false" [accept-info]="''"></civcast-aws-uploader>
        </div>
        }
        <button class="btn btn-primary" (click)="removeSettings()" *ngIf="importSettings">Remove Saved
          Settings</button>
      </div>
      <!--everything else-->
      <div>
        @switch (currentStep) {
        @case (1) {
        <!-- case 1 -->
        <div class="border p-3 mb-3">
          <!-- step title -->
          <h2 class="page-title fs-4">Step 1</h2>
          <!-- worksheet selector -->
          <div class="mb-3">
            <label class="form-label">Which worksheet inside your Excel file do you want to use?</label>
            <select class="form-select w-50" [(ngModel)]="employeeImportRequest.WorkSheetName">
              @for ( ws of employeeImportResponse?.Worksheets; track $index) {
              <option [ngValue]="ws">
                {{ ws }}
              </option>
              }

            </select>
          </div>
          <!--instructions -->
          <div class="mb-3">For each field below, tell us which column in your Excel file contains that data.</div>
          <!--employee ID -->
          <div class="mb-3">
            <label class="form-label">Employee Id</label>
            <select class="form-select w-50" [(ngModel)]="employeeImportRequest.EmployeeId.Cell">
              @for (number of letterArray; track $index) {
              <option [ngValue]="number">
                {{ number }}
              </option>
              }
            </select>
          </div>
          <!-- first name -->
          <div class="mb-3">
            <label class="form-label">First Name</label>
            <select class="form-select w-50" [(ngModel)]="employeeImportRequest.FirstName.Cell">
              @for (number of letterArray; track $index) {
              <option [ngValue]="number">
                {{ number }}
              </option>
              }

            </select>
          </div>
          <!-- last name -->
          <div class="mb-3">
            <label class="form-label">Last Name</label>
            <select class="form-select w-50" [(ngModel)]="employeeImportRequest.LastName.Cell">
              @for (number of letterArray; track $index) {
              <option [ngValue]="number">
                {{ number }}
              </option>
              }
            </select>
          </div>
          <!-- classification -->
          <div class="mb-3">
            <label class="form-label">Classification</label>
            <select class="form-select w-50" [(ngModel)]="employeeImportRequest.Classification.Cell">
              @for (number of letterArray; track $index) {
              <option [ngValue]="number">
                {{ number }}
              </option>
              }
            </select>
          </div>
          <!--header row? -->
          <div class="form-check">
            <input class="form-check-input" type="checkbox" [(ngModel)]="employeeImportRequest.HasHeader" />
            <label class="form-check-label" for="flexCheckDefault">
              Has Header Row?
            </label>
          </div>
        </div>
        } @case (2) {
        <div>
          <!-- case 2 -->
          <div class="border p-3 mb-3">
            <!-- step title -->
            <h2 class="page-title fs-4">Step 2</h2>
            <!--instructions -->
            <div class="mb-3">
              Does the employee information below look correct? If not, return to the previous step.
            </div>
            <!--sample -->
            <div class="bg-light p-3">
              <div class="mb-3">
                <label class="fw-bold me-2">Employee Id:</label>
                <span>{{ sampleEmployee?.CustomId }}</span>
              </div>
              <div class="mb-3">
                <label class="fw-bold me-2">First Name:</label>
                <span>{{ sampleEmployee?.FirstName }}</span>
              </div>
              <div class="mb-3">
                <label class="fw-bold me-2">Last Name:</label>
                <span>{{ sampleEmployee?.LastName }}</span>
              </div>
              <div class="mb-3">
                <label class="fw-bold me-2">Classification:</label>
                <span>{{ sampleEmployee?.Classification }}</span>
              </div>
            </div>
          </div>



        </div>
        } @case (3) {
        <!-- case 3 -->
        <div class="border p-3 mb-3">
          <!-- step title -->
          <h2 class="page-title fs-4">Step 3</h2>
          <!--instructions -->
          <div class="mb-3">
            Do you want us to remember these import settings for next time?
          </div>
          <!--radio buttons - yes/no -->
          <div>
            <div class="form-check">
              <input class="form-check-input" name="settings" type="radio" [(ngModel)]="saveSettings" [value]="true"
                [checked]="saveSettings==true" />
              <label class="form-check-label" for="flexRadioDefault1">
                Yes
              </label>
            </div>
            <div class="form-check">
              <input class="form-check-input" name="settings" type="radio" [(ngModel)]="saveSettings" [value]="false"
                [checked]="saveSettings==false" />
              <label class="form-check-label" for="flexRadioDefault1">
                No
              </label>
            </div>
          </div>
        </div>
        }@case (4) {
        <!-- case 4 -->
        <div class="border p-3 mb-3">
          <!-- no changes detected -->
          <div class="alert alert-info" role="alert"
            *ngIf="importInfo?.EmployeeValidation?.length === 0 && importInfo?.NewEmployees?.length === 0">
            No changes detected in the import.
          </div>
          <!-- updating employees -->
          <div *ngIf="updatingEmployees">
            <div class="progress" *ngIf="updatingEmployees">
              <div class="progress-bar progress-bar-striped progress-bar-animated progress-bar-success"
                role="progressbar" [ngStyle]="{ 'width': '100%' }"></div>
            </div>
            <label style="text-align: center;">Processing Employees</label>
          </div>
          <!-- the stuff -->
          <div *ngIf="!updatingEmployees">
            <!-- step title -->
            <div class="mb-3" *ngIf="importInfo?.EmployeeValidation">
              <h2 class="page-title fs-4">Employee Validation</h2>
              <!--instructions -->
              <div class="mb-3">
                We discovered some changes with the employees below. You can edit these employees if you want.
              </div>
              <ul class="list-group" *ngFor="let emp of importInfo?.EmployeeValidation; let i = index">
                <li class="list-group-item">
                  <div class="row">
                    <div class="col-9">
                      <div class="fw-bold">{{emp.Employee.CustomId}} - {{ emp.Employee.FirstName }} {{
                        emp.Employee.LastName }}</div>
                      <div class="text-muted" *ngFor="let cInfo of emp.Validation">
                        <ng-container *ngFor="let info of cInfo.ChangeInfo">
                          {{ info }}
                        </ng-container>
                      </div>
                      <div class="mt-3" *ngIf="emp.IsVerify">
                        <div class="form-floating mb-3">
                          <input class="form-control" type="text" [(ngModel)]="emp.Employee.CustomId" />
                          <label for="floatingInput">Number</label>
                        </div>
                        <div class="form-floating mb-3">
                          <input class="form-control" [(ngModel)]="emp.Employee.FirstName" />
                          <label for="floatingInput">First Name</label>
                        </div>
                        <div class="form-floating mb-3">
                          <input class="form-control" [(ngModel)]="emp.Employee.LastName" />
                          <label for="floatingInput">Last Name</label>
                        </div>
                        <div class="form-floating mb-3">
                          <input class="form-control" [(ngModel)]="emp.Employee.Classification" />
                          <label for="floatingInput">Classification</label>
                        </div>
                      </div>
                    </div>
                    <div class="col-3 d-flex justify-content-end">
                      <div>
                        <button class="btn btn-outline-secondary" (click)="fixData(emp)">Edit</button>
                      </div>
                    </div>
                  </div>
                </li>
              </ul>
            </div>
            <!-- save button -->
            <div class="d-flex justify-content-end" *ngIf="importInfo?.EmployeeValidation || importInfo?.NewEmployees">
              <button type="button" class="btn btn-primary" (click)="saveEmployees()">Finish Import and Save
                Changes</button>
            </div>
          </div>
        </div>
        }
        }
        <!-- buttons -->
        <div class="d-flex justify-content-end">
          @if(currentStep > 0 && currentStep < 4){ <div class="btn-group">
            <button type="button" class="btn btn-primary" (click)="goToPreviousStep()"
              [disabled]="currentStep === minStep">Previous Step</button>
            <button type="button" class="btn btn-primary" (click)="goToNextStep()"
              [disabled]="currentStep === maxStep">Next
              Step</button>
        </div>
        }
      </div>
    </div>
</div>
</section>
</div>