export interface BidOpsDocument{
	FileId: string
	Title: string;
	Description: string;
	DateTime: string;
	Category: string;
	NumberOfPages: number;
}

export interface DocumentDownloadHistory{
	DateCreated: string;
	FileId: string;
	Id: string;
	IpAddress:string;
	ProjetId: string;
	UserId: string;
	Phone: string;
	Email: string;
	CompanyName: string;
	FirstName: string;
	LastName: string;
	FileTitle: string;
	FileType: string;
	Fax: string;

}

export interface DocumentDownloadHistoryInfo{
	History: DocumentDownloadHistory;
	User: DownloadHistoryUserInfo;
	FileInfo: BidOpsDocument;
}

export interface DownloadHistoryUserInfo{
	FirstName: string;
	LastName: string;
	PhoneNumber: string;
	FaxNumber:string;
}
