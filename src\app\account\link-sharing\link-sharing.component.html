<div class="container p-4">
	<!-- header -->
	<header class="mb-3">
		<h1 class="fs-5 mb-3">Project Link Sharing</h1>
		<p class="mb-0">This page gives you ready-made links to share your current bidding projects. There are two
			types of links: one for sending in emails and another for posting on your website. Just copy the link you
			need and share it!</p>
	</header>
	<!--link sharing -  webpage -->
	<section class="card mb-4">
		<div class="card-body">
			<h2 class="card-title fs-6">Webpage</h2>
			@if(isLoading()){
			<div class="placeholder-glow">
				<div class="placeholder col-12" style="height: 35px;"></div>
			</div>
			}@else {
			<div class="input-group">
				<input class="form-control text-secondary" type="text" [ngModel]="webPageLink()" readonly>
				<button class="btn btn-outline-primary" type="button" (click)="copyInput(webPageLink())"><i
						class="far fa-copy"></i></button>
			</div>
			}
		</div>
	</section>
	<!--link sharing - email -->
	<section class="card mb-4">
		<div class="card-body">
			<h2 class="card-title fs-6">Email</h2>
			@if(isLoading()){
			<div class="placeholder-glow">
				<div class="placeholder col-12" style="height: 35px;"></div>
			</div>
			}@else {
			<div class="input-group">
				<input class="form-control text-secondary" type="text" [ngModel]="emailLink()" readonly>
				<button class="btn btn-outline-primary" (click)="copyInput(emailLink())"><i
						class="far fa-copy"></i></button>
			</div>
			}
		</div>
	</section>
	<!-- vanity url -->
	<section class="card">
		<div class="card-body">
			<h2 class="card-title fs-6">Branded URL</h2>
			<form [formGroup]="generalSettingsForm" (ngSubmit)="updateVanityId()">
				<div class="mb-2">
					<label class="form-label">
						Enter your organization’s name to create a branded URL.
					</label>
					<app-vanity-id-input formControlName="VanityId" [maxLength]="vanityIdMaxLength">
					</app-vanity-id-input>
					@if(vanityErrorMessage()){
					<div class="alert alert-danger mt-2" role="alert">
						{{ vanityErrorMessage() }}
					</div>
					}
				</div>
				<div class="d-flex justify-content-end">
					<button class="btn btn-primary" type="submit"
						[disabled]="isSaving() || !generalSettingsForm.valid || !generalSettingsForm.dirty">
						@if (isSaving()) {
						<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span>
						}
						Save
					</button>
				</div>
			</form>
		</div>
	</section>
</div>