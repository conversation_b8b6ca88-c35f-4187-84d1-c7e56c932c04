import { CommonModule } from '@angular/common';
import { Component, OnInit, inject } from '@angular/core';
import { FormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { NgbDateStruct } from '@ng-bootstrap/ng-bootstrap';
import { Subject } from 'rxjs';
import { debounceTime, distinctUntilChanged } from 'rxjs/operators';
import { AdvertisementRequest } from 'src/app/bid-adv/shared/interfaces/advertisement-request';
import { DateTimePickerComponent } from 'src/app/shared/ui/date-time-picker/date-time-picker.component';
import { BidsAdvService } from 'src/app/bid-adv/shared/data-access/bids.service';

@Component({
    selector: 'app-bids-filter',
    templateUrl: './bids-filter.component.html',
    styleUrls: ['./bids-filter.component.css'],
    imports: [CommonModule, FormsModule, DateTimePickerComponent]
})
export class BidsFilterComponent implements OnInit {
  router = inject(Router);
  advertisementService = inject(BidsAdvService);  
  aRoute = inject(ActivatedRoute);
  searchInputDelay = 600;
  selectedDate: NgbDateStruct | null = null;
  searchInputChange = new Subject<string>();
  timeOptions: Array<any> = new Array<any>();
  selectedTimeOption: any = null;
  search = this.advertisementService.search;

  constructor() {

    this.setupBindings();
    this.setupTimeOptions();

    this.aRoute.queryParams.subscribe(params => {
      if (params['currentPage']) {
        //this.advertisementRequest.currentPage = parseInt(params['currentPage']);
        this.advertisementService.currentPage.set(parseInt(params['currentPage']));
      } else {
        this.advertisementService.currentPage.set(1);
        //this.advertisementRequest.currentPage = 1;
      }

      if (params['limit']) {
        this.advertisementService.limit.set(parseInt(params['limit']));        
      } else {
        this.advertisementService.limit.set(50);        
      }

      if (params['sortBy']) {
        this.advertisementService.sortBy.set(params['sortBy']);
      } else {
        this.advertisementService.sortBy.set("BidDetails.BidDateTimeInfo.Date");

        this.router.navigate([], {
          queryParams: { sortBy: "BidDetails.BidDateTimeInfo.Date" },
          skipLocationChange: false,
          replaceUrl: true,
          queryParamsHandling: 'merge'
        });
      }

      if (params['sortOrder']) {
        this.advertisementService.sortOrder.set(params['sortOrder']);        
      } else {        
        this.advertisementService.sortOrder.set("desc");
        this.router.navigate([], {
          queryParams: { sortOrder: "desc" },
          skipLocationChange: false,
          replaceUrl: true,
          queryParamsHandling: 'merge'
        });
      }

      if (params['search']) {
        this.advertisementService.search.set(params['search']);        
      } else {
        this.advertisementService.search.set(null);         
      }

      if (params['bidDateStart']) {
        this.advertisementService.bidDateStart.set(params['bidDateStart']);        
      } else {
        this.advertisementService.bidDateStart.set(null);        
      }

      if (params['bidDateEnd']) {
        this.advertisementService.bidDateEnd.set(params['bidDateEnd']);        
      } else {
        this.advertisementService.bidDateEnd.set(null);        
      }



      if (params['filterTitle']) {
        const toInfo = this.timeOptions.find(x => x.title === params['filterTitle']);
        this.selectedTimeOption = toInfo;

        if (params['filterTitle'] === 'currentdate') {
          var d = new Date(params['bidDateStart']);
          var p = { year: d.getFullYear(), month: d.getMonth() + 1, day: d.getDate() };
          this.selectedDate = { ...p } as NgbDateStruct;          
        }

      } else {
        var defaultOption = this.timeOptions[0]
        this.selectedTimeOption = defaultOption;
        this.advertisementService.bidDateStart.set(defaultOption.startDate);
        this.advertisementService.bidDateEnd.set(defaultOption.endDate);        

        this.router.navigate([], {
          queryParams: {
            bidDateStart: defaultOption.startDate,
            bidDateEnd: defaultOption.endDate,
            releaseDateStart: null,
            releaseDateEnd: null,
            filterTitle: defaultOption.title
          },
          skipLocationChange: false,
          replaceUrl: true,
          queryParamsHandling: 'merge'
        })
      } 

    });

  }

  setupBindings() {
    this.searchInputChange
      .pipe(debounceTime(this.searchInputDelay), distinctUntilChanged())
      .subscribe(value => {
        if (value === '') {
          this.advertisementService.search.set(null);
        } else {
          this.advertisementService.search.set(value);
        }

        this.router.navigate([], {
          queryParams: { search: value },
          queryParamsHandling: "merge"
        });
      });
  }

  ngOnInit() {

  }

  setupTimeOptions() {
    var nDate = new Date(new Date().setHours(0, 0, 0, 0));

    var currentFilter = {
      title: 'Current',
      type: 'BidDate',
      startDate: `${nDate.getFullYear()}-${nDate.getMonth() + 1}-${nDate.getDate()}`,
      endDate: null
    }

    var allFilter = {
      title: 'All',
      type: null,
      startDate: null,
      endDate: null
    }

    this.timeOptions.push(currentFilter);
    this.timeOptions.push(allFilter);

    this.selectedTimeOption = this.timeOptions[0];
  }

  selectTime(option: any) {
    this.router.navigate([], {
      queryParams: {
        bidDateStart: option.startDate,
        bidDateEnd: option.endDate,
        filterTitle: option.title
      },
      queryParamsHandling: "merge"
    });

    this.selectedDate = null;
  }

  onDateSelect(dateInfo: any) {
    this.selectedDate = dateInfo;

    this.router.navigate([], {
      queryParams: {
        bidDateStart: `${this.selectedDate?.year}-${this.selectedDate?.month}-${this.selectedDate?.day}`,
        bidDateEnd: `${this.selectedDate?.year}-${this.selectedDate?.month}-${this.selectedDate?.day}`,
        filterTitle: 'currentdate'
      },
      queryParamsHandling: "merge"
    });
  }
}