import { Injectable, computed, effect, inject, signal } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, Validators } from '@angular/forms';
import { BidOpsDocumentService } from '../../shared/data-access/bid-ops-document.service';
import { AuthService } from 'src/app/shared/data-access/auth.service';
import { ToastrService } from 'ngx-toastr';
import { ProjectFileInfo } from '../../shared/interfaces/bid-ops-documents';
import { HelperTools } from 'src/app/shared/utils/helper-tools';
import { CarrierOption, OrderSubmissionData, PlanOrder, PlanOrderFormItem, PlanOrderItem, SizeOption } from '../../shared/interfaces/plan-order';
import { HttpClient } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { PlanOrderInvoicesService } from '../../../account/plan-order-invoices/plan-order-invoices.service';
import { PlanOrderInvoiceService } from '../../../account/plan-order-invoices/plan-order-invoice/plan-order-invoice.service';
import { rxResource } from '@angular/core/rxjs-interop';
import { PlanOrdersDataService } from './plan-orders-data.service';


@Injectable()
export class PlanOrdersService {

  /////////// Dependencies //////////

  private readonly bidOpsDocumentService = inject(BidOpsDocumentService);
  private readonly planOrderDataService = inject(PlanOrdersDataService);
  private readonly authService = inject(AuthService);
  private readonly toastrService = inject(ToastrService);
  private readonly http = inject(HttpClient);
  private readonly fb = inject(FormBuilder);
  private readonly planOrderInvoicesService = inject(PlanOrderInvoicesService);
  private readonly planOrderInvoiceService = inject(PlanOrderInvoiceService);  
  
  /////////// State Signals //////////
  private readonly _planOrderItems = signal<PlanOrderItem[]>([]);
  private readonly _isSubmittingOrder = signal<boolean>(false);
  private readonly _isDownloading = signal<boolean>(false);
  private readonly _lastSubmittedOrder = signal<OrderSubmissionData[] | null>(null);
  private readonly _formUpdateTrigger = signal<number>(0); private readonly _orderForm = this.fb.group({
    items: this.fb.array([]),
    name: ['', Validators.required],
    company: [''],
    address1: ['', Validators.required],
    address2: [''],
    city: ['', Validators.required],
    state: ['', Validators.required],
    zipCode: ['', Validators.required],
    carrier: ['', Validators.required],
    accountNumber: ['', Validators.required],
    orderNotes: [''],
    poNumber: [''],
    emailReceipt: ['', Validators.required],
    directPhone: ['', Validators.required]
  });
  readonly projectId = signal<string | null>(null);
  projectTitle = signal<string | null>(null);
  tax = computed(() => this.planOrderDataService.planOrderData()?.TaxRate || 0.085); // Default to 8.5% if not available

  /////////// Computed State //////////

  readonly isAuthenticated = computed(() => this.authService.isLoggedInSignal());
  readonly isLoading = computed(() => this.bidOpsDocumentService.isLoading());
  readonly purchasableDocuments = computed(() => {
    const allFiles = this.bidOpsDocumentService.allFiles();
    return allFiles.filter(file =>
      this.purchasableCategories.includes(file.File.Category?.Group || '')
    );
  }); 
  readonly planOrderItems = computed(() => this._planOrderItems());
  readonly isSubmittingOrder = computed(() => this._isSubmittingOrder());
  readonly isDownloading = computed(() => this._isDownloading());
  readonly lastSubmittedOrder = computed(() => this._lastSubmittedOrder());

  readonly orderSubtotal = computed(() => {
    // Trigger re-computation when form values change
    this._formUpdateTrigger();
    const items = this._planOrderItems();
    return items.reduce((total, item) => {
      const formValue = item.FormGroup.value;
      const pageCount = item.Document.File.NumberOfPages || 1;
      const price = this.getDocumentPrice(formValue.selectedSize, pageCount);
      const quantity = parseInt(formValue.selectedQuantity, 10) || 0;
      return total + (price * quantity);
    }, 0);
  });
  
  readonly taxAmount = computed(() => {
    // Calculate tax as 8.5% of subtotal
    const subtotal = this.orderSubtotal();
    return subtotal * this.tax();
  });
  
  readonly orderTotal = computed(() => {
    // Trigger re-computation when form values change
    this._formUpdateTrigger();
    const subtotal = this.orderSubtotal();
    const tax = this.taxAmount();
    return subtotal + tax;
  });
  readonly totalQuantity = computed(() => {
    // Trigger re-computation when form values change
    this._formUpdateTrigger();
    const items = this._planOrderItems();
    return items.reduce((sum, item) => {
      const formValue = item.FormGroup.value;
      const quantity = parseInt(formValue.selectedQuantity, 10) || 0;
      return sum + quantity;
    }, 0);
  });
  readonly itemsFormArray = computed(() => this._orderForm.get('items') as FormArray);
  readonly orderForm = computed(() => this._orderForm);

  /////////// Constants //////////


  readonly availableSizes = computed(() => this.planOrderDataService.planOrderData()?.AvailableSizes || []);
  readonly availableCarriers = computed(() => this.planOrderDataService.planOrderData()?.AvailableCarriers || []);
  readonly purchasableCategories: string[] = [
    "BidDoc",
    "Plan",
    "Addenda",
    "PlanRevision",
    "Award",
    "Other"
  ];

  readonly availableQuantities = Array.from({ length: 12 }, (_, i) => i + 1);

  
  constructor() {
    // Auto-initialize order items when purchasable documents are available
    effect(() => {
      if (this.projectId()) {
        this.bidOpsDocumentService.projectId.set(this.projectId());
      }
    });
  }
  /////////// Public Methods //////////

  /**
   * Trigger form update for computed values
   */
  private triggerFormUpdate(): void {
    this._formUpdateTrigger.set(this._formUpdateTrigger() + 1);
  }
  /**
   * Get the price per page for a specific size
   */
  getSizePricePerPage(size: string): number {
    const sizeOption = this.availableSizes()?.find(s => s.Value === size);
    return sizeOption ? sizeOption.PricePerPage : 0;
  }

  /**
   * Get the total price for a document based on size and page count
   */
  getDocumentPrice(size: string, pageCount: number): number {
    const pricePerPage = this.getSizePricePerPage(size);
    return pricePerPage * (pageCount || 1);
  }

  /**
   * Get the label for a specific size
   */
  getSizeLabel(size: string): string {
    const sizeOption = this.availableSizes()?.find(s => s.Value === size);
    return sizeOption ? sizeOption.Label : size;
  }

  /**
   * Get the unit price for an order item (price per document copy)
   */  getItemPrice(item: PlanOrderItem): number {
    const formValue = item.FormGroup.value;
    const pageCount = item.Document.File.NumberOfPages || 1;
    return this.getDocumentPrice(formValue.selectedSize, pageCount);
  }
  /**
   * Get the total price for an order item (price * quantity)
   */  getItemTotal(item: PlanOrderItem): number {
    const formValue = item.FormGroup.value;
    const pageCount = item.Document.File.NumberOfPages || 1;
    const price = this.getDocumentPrice(formValue.selectedSize, pageCount);
    const quantity = parseInt(formValue.selectedQuantity, 10) || 0;
    return price * quantity;
  }

  /**
   * Remove an item from the order
   */
  removeItem(itemIndex: number): void {
    const items = this._planOrderItems();
    if (items[itemIndex]) {
      // Remove from form array
      this.itemsFormArray().removeAt(itemIndex);

      // Remove from items array
      items.splice(itemIndex, 1);
      this._planOrderItems.set([...items]);
    }
  }
  /**
   * Download a document
   */
  downloadDocument(item: PlanOrderItem): void {
    if (!this.isAuthenticated()) {
      this.toastrService.error('Please log in to download documents', 'Authentication Required');
      return;
    }

    const projectId = this.projectId();
    if (!projectId) {
      this.toastrService.error('Project information not available', 'Error');
      return;
    }

    this._isDownloading.set(true);

    // Use the existing document service to download
    this.bidOpsDocumentService.downloadFile(
      projectId,
      item.Document.File.FileId || '',
      item.Document.File.Title || 'document'
    ).subscribe({
      next: (response) => {
        HelperTools.downloadFile(response.PreSignedUrl, item.Document.File.Title || 'document');
        this.toastrService.success(`Downloaded: ${item.Document.File.Title}`, 'Download Complete');
        this._isDownloading.set(false);
      },
      error: (error) => {
        console.error('Download error:', error);
        this.toastrService.error('Failed to download document', 'Download Error');
        this._isDownloading.set(false);
      }
    });
  }  /**
   * Submit the order
   */
  submitOrder(): void {
    if (!this.isAuthenticated()) {
      this.toastrService.error('Please log in to submit an order', 'Authentication Required');
      return;
    }

    if (this._planOrderItems().length === 0) {
      this.toastrService.warning('No items in your order', 'Empty Order');
      return;
    }

    this._isSubmittingOrder.set(true);

    // // Get form values for order submission
    const orderData: OrderSubmissionData[] = this._planOrderItems().map((item) => ({
      Document: item.Document,
      SelectedSize: item.FormGroup.value.selectedSize,
      SelectedQuantity: parseInt(item.FormGroup.value.selectedQuantity, 10),
      Price: this.getItemPrice(item),
      Total: this.getItemTotal(item)
    }));

    // Create PlanOrder object from form data
    const formValue = this._orderForm.value;
    const planOrder: PlanOrder = {
      Id: undefined,
      OrderNumber: undefined,
      OrderDate: new Date().toISOString(),
      Name: formValue.name || '',
      Email: formValue.emailReceipt || '',
      Phone: formValue.directPhone || '',
      Subtotal: this.orderSubtotal(),
      Tax: this.tax(),
      TaxAmount: this.taxAmount(),
      Shipping: undefined,
      Total: this.orderTotal(),
      ProjectTitle: this.projectTitle() || undefined,
      ProjectId: this.projectId() || undefined,
      Items: this.convertOrderSubmissionDataToPlanOrderFormItems(orderData),
      ShippingAddress: {
        Name: formValue.name || '',
        Company: formValue.company || undefined,
        Address1: formValue.address1 || '',
        Address2: formValue.address2 || undefined,
        City: formValue.city || '',
        State: formValue.state || '',
        ZipCode: formValue.zipCode || ''
      },
      Carrier: formValue.carrier || 'unknown',
      AccountNumber: formValue.accountNumber || 'unknown',
      OrderNotes: formValue.orderNotes || undefined,
      PoNumber: formValue.poNumber || undefined,
      EmailReceipt: formValue.emailReceipt || 'unknown',
      DirectPhone: formValue.directPhone || 'unknown'
    };


    this.http.post<PlanOrder>(environment.services_root_endpoints.bidops_planorders, planOrder).subscribe({
      next: (response) => {
        this._isSubmittingOrder.set(false);
        this._lastSubmittedOrder.set(orderData);

        // Refresh invoice services to get updated data
        this.planOrderInvoicesService.addInvoice(response);

        this.toastrService.success(
          `Order submitted successfully! Total: $${this.orderTotal().toFixed(2)}`,
          'Order Submitted'
        );

        // Clear the order after successful submission
        this.clearPlanOrderItems();
      },
      error: (error) => {
        this._isSubmittingOrder.set(false);
        console.error('Order submission error:', error);
        // Handle error response
      }
    });

  }/**
   * Add a document to the order
   */
  addDocumentToOrder(document: ProjectFileInfo): void {
    const items = this._planOrderItems();
    const existingItem = items.find(item => item.Document.File.FileId === document.File.FileId);

    if (existingItem) {
      this.toastrService.warning('Document is already in your order', 'Already Added');
      return;
    }

    const formGroup = this.createItemFormGroup(this.getBestSizeForDocument(document));
    this.itemsFormArray().push(formGroup); const newItem: PlanOrderItem = {
      Document: document,
      FormGroup: formGroup
    };

    this._planOrderItems.set([...items, newItem]);
    this.toastrService.success(`Added "${document.File.Title}" to order`, 'Document Added');
  }

  /**
   * Check if a document is already in the order
   */
  isDocumentInOrder(document: ProjectFileInfo): boolean {
    const items = this._planOrderItems();
    return items.some(item => item.Document.File.FileId === document.File.FileId);
  }

  /////////// Private Methods //////////
  /**
   * Create a form group for an order item
   */
  private createItemFormGroup(defaultSize: string = '11x17', defaultQuantity: number = 1): FormGroup {
    const formGroup = this.fb.group({
      selectedSize: [defaultSize],
      selectedQuantity: [defaultQuantity]
    });

    // Subscribe to value changes to trigger computed updates
    formGroup.valueChanges.subscribe(() => {
      this.triggerFormUpdate();
    });

    return formGroup;
  }

  /**
   * Initialize the plan order items based on purchasable documents
   */
  private initializePlanOrderItems(): void {
    const purchasableDocs = this.purchasableDocuments();

    // Clear existing form array
    const formArray = this.itemsFormArray();
    while (formArray.length !== 0) {
      formArray.removeAt(0);
    }

    const items: PlanOrderItem[] = purchasableDocs.map(doc => {
      const formGroup = this.createItemFormGroup();
      formArray.push(formGroup); return {
        Document: doc,
        FormGroup: formGroup
      };
    }); this._planOrderItems.set(items);
  }  /**
   * Convert OrderSubmissionData to PlanOrderFormItem format
   * This is useful when creating invoices from submitted orders
   */
  convertOrderSubmissionDataToPlanOrderFormItems(orderData: OrderSubmissionData[]): PlanOrderFormItem[] {
    return orderData.map(item => ({
      SelectedSize: item.SelectedSize,
      SelectedQuantity: Number(item.SelectedQuantity),
      FileId: item.Document.File.FileId || undefined,
      Title: item.Document.File.Title || undefined,
      Category: item.Document.File.Category?.Name || undefined,
      NumberOfPages: item.Document.File.NumberOfPages || undefined,
      PricePerPage: this.getSizePricePerPage(item.SelectedSize),
      Total: item.Total
    }));
  }

  /**
   * Clear all plan order items from the current order
   */
  private clearPlanOrderItems(): void {
    // Clear the form array
    const formArray = this.itemsFormArray();
    while (formArray.length !== 0) {
      formArray.removeAt(0);
    }

    // Clear the plan order items signal
    this._planOrderItems.set([]);
    
    // Trigger form update to recalculate totals
    this.triggerFormUpdate();
  }

  /**
   * Get the best matching size option based on document dimensions
   */
  private getBestSizeForDocument(document: ProjectFileInfo): string {
    const dimensions = document.File.Dimensions;
    if (!dimensions || !dimensions.X || !dimensions.Y) {
      return '8.5x11'; // Default fallback
    }

    return `${dimensions.X}x${dimensions.Y}`; // Return as "width x height" format

  }
}
