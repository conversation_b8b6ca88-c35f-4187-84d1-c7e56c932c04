

export interface PresignedUrlResponse {
	PreSignedUrl: string;
	Bucket: string;
}

export class ProjectFile {
	FileId: string | null = null;
	Title: string | null = null;
	DateStamp: string | null = null;
	Storage: Storage | null = null;
	Category: DocumentCategory | null = null;
	Dimensions: Dimension | null = null;
	NumberOfPages: number | null = null;
	ConfirmRequired: boolean = false;
	IsDownloaded: boolean | null = null;
}

export interface Storage {
	FileName: string | null;
	FileExtension: string | null;
	StorageMetaData: DocMetadata | null;
}

export interface DocMetadata {
	NumberOfPages: number | null;
	PrintInfo: Dimension | null;
}

export interface Category {
}

export interface Dimension {
	X: number | null;
	Y: number | null;
	Display: string;
}

export interface DocumentCategory {

	Name: string;
	Group: string;
	PrintLocked: boolean;
	IsPurchasable: boolean;
}


export interface BidOpsDocumentUrlResponse {
	PreSignedUrl: string | null;
}

export interface GetDocumentInfoResponse {
	MetaData: PdfDocumentMetaData;
}

export interface PdfDocumentMetaData {
	NumberOfPages: number | null;
	NeedsReview: boolean;
	PageSize: Dimension | null;
}


export interface FileInfo {
	File: any;
	Key: string | null;
	Name: string;
	Size: number;
	isComplete: boolean;
	isProgressing: boolean;
	uploadProgress: number;
	FileId: string;
	Extension: string;
}

export interface Action {
	Name: string;
	Description: string;
	State: string;
}

export interface DocInfo {
	IsEditing: boolean;
	IsDeleting: boolean;
	IsPurchasable: boolean;
	IsUpdating: boolean;
	IsDownloading: boolean;
	SelectedCategory: DocumentCategory | null;
	SelectedDimension: Dimension | null;
	Doc: ProjectFile | null;
	DocEditing: ProjectFile | null;
}

