import { Advertisement } from "./advertisement";

export class ProjectSaveOptions{
	constructor(project: Advertisement, addOnlineBid: boolean, paymentType: string){
		this.Project = project;
		this.AddOnlineBid = addOnlineBid;
		this.PaymentType = paymentType;
	}
	Project: Advertisement;
	AddOnlineBid: boolean;
	PaymentType: string;

}

export const ProjectConstants = {
	LOW_BID_NAME: 200,
	PROJECT_TITLE_LENGTH: 300,	
	INTERNALID: 500,
	DETAILS_ESTIMATE: 500,
	TYPE_OF_WORK: 500,
	OWNER: 500,
	LOCATION: 200,
	MAP_CODE: 200,
	DETAILS_ESTIMATE_LENGTH: 50,
	MAX_DEFAULT: 1000,
	SCOPE: 20000,
	NOTES: 2000,
	DEFAULT_CONTACT_INFO: 200,
	PHONE_MAX: 20,
	EMAIL: 200,
	PASSWORD: 50
}

export const MapConstants = {
	lat: 29.778116880069135, 
	lng: -95.37075775689364,
	zoom: 15
}