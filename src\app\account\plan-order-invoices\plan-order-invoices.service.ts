import { Injectable, computed, inject, signal } from '@angular/core';
import { HttpClient, HttpParams } from '@angular/common/http';
import { environment } from 'src/environments/environment';
import { rxResource } from '@angular/core/rxjs-interop';
import { of } from 'rxjs';
import { PlanOrder, PlanOrderGrid } from '../../bid-opportunities/shared/interfaces/plan-order';

@Injectable({
  providedIn: 'root'
})
export class PlanOrderInvoicesService {
  private readonly client = inject(HttpClient);
  
  /////////// State Signals //////////
  private readonly _initialize = signal<boolean>(false);
  
  /////////// Computed State //////////
  readonly receipts = computed(() => this.receiptsResource.value()?.PlanOrders ?? []);
  readonly total = computed(() => this.receiptsResource.value()?.TotalCount ?? 0);
  readonly isLoading = computed(() => this.receiptsResource.isLoading());
  readonly error = computed(() => this.receiptsResource.error());
  readonly currentPage = signal<number>(1);
  readonly limit = signal<number>(10); // Default limit for pagination
  /////////// rxResource //////////
  private readonly receiptsResource = rxResource({
    request: () => ({ 
      initialized: this.initialize(),
      currentPage: this.currentPage(),
      limit: this.limit()
    }),
    loader: (request) => {
      if (request.request.initialized) {
        
        var httpParams = new HttpParams()
          .set('currentPage', request.request.currentPage.toString())
          .set('limit', request.request.limit.toString());


        return this.client.get<PlanOrderGrid>(`${environment.services_root_endpoints.bidops_planorders}`, {params: httpParams});
      }

      return of(null); // Return empty array if not initialized
    }
  });
  
  /////////// Public Properties //////////
  readonly initialize = computed(() => this._initialize());
  
  /////////// Public Methods //////////
  
  /**
   * Initialize the service to load receipts
   */
  loadReceipts(): void {
    this._initialize.set(true);
  }
  
  clearReceipts(): void {
    this._initialize.set(false);
  }
  /**
   * Refresh receipts data
   */
  refreshReceipts(): void {
    this.receiptsResource.reload();
  }

  addInvoice(receipt: PlanOrder): void {
    this.receiptsResource.update((current) => {
      return { PlanOrders: [...current?.PlanOrders || [], receipt], TotalCount: current?.TotalCount ?? 0 + 1 };
    });
  }

  /**
   * Get receipt by ID
   */
  getReceiptById(receiptId: string): PlanOrder | undefined {
    return this.receipts().find(receipt => receipt.Id === receiptId);
  }
}
