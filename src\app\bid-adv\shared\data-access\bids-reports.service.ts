import { HttpClient } from "@angular/common/http";
import { Injectable } from "@angular/core";
import { Observable } from "rxjs";
import { AuthService } from "src/app/shared/data-access/auth.service";
import { environment } from "src/environments/environment";
import { ReportBuilderRequest } from "../interfaces/bids-reports";

@Injectable()
export class AdvReportsService {
  constructor(private authService: AuthService, private http: HttpClient) {}

 public RunReportBuilderReport(request: ReportBuilderRequest): Observable<any>{

    var reportInfo = {
      "ReportRequestData": JSON.stringify(request),
      "ReportSQSUrl": environment.reports.adv_reports.report_builder.sqs_url,
      "ReportName": environment.reports.adv_reports.report_builder.name
    }
    return this.http.post(`${environment.services_root_endpoints.reports.service}/reports-service`, reportInfo);

  }

  public GetReportInfo(reportId:string): Observable<any>{
    return this.http.get(`${environment.services_root_endpoints.reports.service}/reports-service/${reportId}`);
  }
}