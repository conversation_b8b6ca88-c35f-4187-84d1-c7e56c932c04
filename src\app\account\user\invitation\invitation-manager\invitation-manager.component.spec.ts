import { ComponentFixture, TestBed, waitForAsync } from '@angular/core/testing';

import { InvitationManagerComponent } from './invitation-manager.component';

describe('DelegateInvitationComponent', () => {
  let component: InvitationManagerComponent;
  let fixture: ComponentFixture<InvitationManagerComponent>;

  beforeEach(waitForAsync(() => {
    TestBed.configureTestingModule({
      declarations: [ InvitationManagerComponent ]
    })
    .compileComponents();
  }));

  beforeEach(() => {
    fixture = TestBed.createComponent(InvitationManagerComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
