import { Component, inject, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ConstructionDailyLogService } from 'src/app/construction/shared/data-access/daily-log-user-project.service';
import { SiteConditionsComponent } from 'src/app/construction/shared/interfaces/daily-log-user-project';
import { toObservable, toSignal } from '@angular/core/rxjs-interop';
import { switchMap, tap } from 'rxjs';
import { CdkDrag, CdkDragDrop, CdkDropList, moveItemInArray } from '@angular/cdk/drag-drop';
import { DailyLogComponentIdentifiers } from 'src/app/construction/shared/interfaces/daily-log-shared';

@Component({
    selector: 'app-site-conditions',
    imports: [CommonModule, FormsModule, CdkDropList, CdkDrag],
    templateUrl: './site-conditions.component.html',
    styleUrl: './site-conditions.component.css'
})
export class ProjectGlobalSettingSiteConditionsComponent {

  dailyLogComponentService = inject(ConstructionDailyLogService);
  siteConditionsComponent = this.dailyLogComponentService.siteConditionsComponent;
  componentIdentifier = signal<DailyLogComponentIdentifiers>(DailyLogComponentIdentifiers.SITE_CONDITIONS);
  condition: string = '';

  previousConditions: Array<string> = [];
  gatherConditions = toSignal(toObservable(this.siteConditionsComponent).pipe(tap({
    next: (component) => {
      if(component){
        this.previousConditions = [...component.Conditions]
      }
    }
  })));

  private getComponent = toSignal(toObservable(this.componentIdentifier).pipe(
    tap(() => this.isLoading.set(true)),
    switchMap((identifier) => this.dailyLogComponentService.getSubComponent(identifier)),
    tap((identifier) => {
      this.isLoading.set(false);
    })
  ));
  isLoading = signal<boolean>(false);  
  
  removeCondition(idx:number) {
    let component = this.siteConditionsComponent() as SiteConditionsComponent;
    component.Conditions.splice(idx, 1);
    this.dailyLogComponentService.updateSubComponent(component).subscribe();
  }

  addCondition() {
    if(this.condition){ 
      let component = this.siteConditionsComponent() as SiteConditionsComponent;

      if(!component.Conditions){
        component.Conditions = new Array<string>();
      }
      
      if(!component.Conditions.find(x => x.toLocaleLowerCase() === this.condition.toLocaleLowerCase())){
        component.Conditions.push(this.condition);
        this.dailyLogComponentService.updateSubComponent(component).subscribe();
      }     
    }    
  }

  conditionInputBoxChange(condition: string, idx: number) {
    let component = this.siteConditionsComponent() as SiteConditionsComponent;

    if(!this.previousConditions.find(x => x.toLocaleLowerCase() === condition.toLocaleLowerCase())){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }else{
      component.Conditions[idx] = this.previousConditions[idx];
    }    
  }

  dropCondition(event: CdkDragDrop<string[]>) {
    let component = this.siteConditionsComponent() as SiteConditionsComponent;
    moveItemInArray(component.Conditions, event.previousIndex, event.currentIndex);

    if(event.previousIndex !== event.currentIndex){
      this.dailyLogComponentService.updateSubComponent(component).subscribe();
    }
    
  }
}
